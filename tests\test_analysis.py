
import pytest
import pandas as pd
from data_analysis.preprocessing import DataPreprocessor
from data_analysis.technical_analysis import TechnicalAnalyzer
from data_analysis.sentiment_analysis import SentimentAnalyzer
from data_analysis.whale_analysis import WhaleAnalyzer

# Sample DataFrame for testing
@pytest.fixture
def sample_klines_df():
    data = {
        'open_time': pd.to_datetime([1, 2, 3, 4, 5], unit='s'),
        'open': [100, 102, 105, 103, 106],
        'high': [103, 106, 107, 105, 108],
        'low': [99, 101, 104, 102, 105],
        'close': [102, 105, 103, 106, 107],
        'volume': [1000, 1200, 1100, 1300, 1050],
        'close_time': pd.to_datetime([1, 2, 3, 4, 5], unit='s'),
        'quote_asset_volume': [100000, 126000, 113300, 137800, 112350],
        'number_of_trades': [100, 120, 110, 130, 105],
        'taker_buy_base_asset_volume': [500, 600, 550, 650, 525],
        'taker_buy_quote_asset_volume': [50000, 63000, 56650, 68900, 56175],
        'ignore': [0, 0, 0, 0, 0]
    }
    return pd.DataFrame(data)

# Test DataPreprocessor
def test_data_preprocessor(sample_klines_df):
    preprocessor = DataPreprocessor()
    df_processed = preprocessor.preprocess_klines(sample_klines_df.copy())
    assert not df_processed.empty
    assert 'open' in df_processed.columns
    assert df_processed.shape == sample_klines_df[['open', 'high', 'low', 'close', 'volume']].shape
    # Check if values are scaled (between 0 and 1)
    assert (df_processed.values >= 0).all() and (df_processed.values <= 1).all()

# Test TechnicalAnalyzer
def test_technical_analyzer_indicators(sample_klines_df):
    analyzer = TechnicalAnalyzer()
    df_with_indicators = analyzer.add_technical_indicators(sample_klines_df.copy())
    assert not df_with_indicators.empty
    assert 'SMA_10' in df_with_indicators.columns
    assert 'RSI_14' in df_with_indicators.columns
    assert 'MACD_12_26_9' in df_with_indicators.columns

def test_technical_analyzer_support_resistance(sample_klines_df):
    analyzer = TechnicalAnalyzer()
    levels = analyzer.identify_support_resistance(sample_klines_df.copy())
    assert 'support' in levels
    assert 'resistance' in levels
    assert isinstance(levels['support'], list)
    assert isinstance(levels['resistance'], list)

# Test SentimentAnalyzer
def test_sentiment_analyzer_text():
    analyzer = SentimentAnalyzer()
    result = analyzer.analyze_text_sentiment("This is a positive statement.")
    assert 'compound' in result
    assert 'note' in result # Because it's a placeholder

def test_sentiment_analyzer_news():
    analyzer = SentimentAnalyzer()
    news_data = [
        {"id": 1, "text": "Good news!"},
        {"id": 2, "text": "Bad news."}
    ]
    df_sentiment = analyzer.analyze_news_sentiment(news_data)
    assert not df_sentiment.empty
    assert 'sentiment' in df_sentiment.columns
    assert len(df_sentiment) == 2

# Test WhaleAnalyzer
def test_whale_analyzer_large_transactions():
    analyzer = WhaleAnalyzer()
    transactions = [
        {"amount": 100},
        {"amount": 10000},
        {"amount": 500}
    ]
    large_txs = analyzer.identify_large_transactions(transactions, 5000)
    assert not large_txs.empty
    assert len(large_txs) == 1
    assert large_txs['amount'].iloc[0] == 10000

def test_whale_analyzer_klines(sample_klines_df):
    analyzer = WhaleAnalyzer()
    # Adjust volume in sample_klines_df to trigger whale movement
    df_with_whale = sample_klines_df.copy()
    df_with_whale.loc[2, 'volume'] = 50000 # Artificially high volume
    
    whale_movements = analyzer.get_whale_movements_from_binance_data(df_with_whale)
    assert not whale_movements.empty
    assert len(whale_movements) >= 1 # At least one whale movement should be detected



