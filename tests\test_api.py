
import pytest
from unittest.mock import patch, MagicMock
import os
from data_collection.binance_api import BinanceAPI

# Mock environment variables for API keys
@pytest.fixture(autouse=True)
def mock_env_vars():
    with patch.dict(os.environ, {
        "BINANCE_API_KEY": "test_api_key",
        "BINANCE_SECRET_KEY": "test_secret_key"
    }):
        yield

# Test BinanceAPI initialization
def test_binance_api_init():
    api = BinanceAPI()
    assert api.api_key == "test_api_key"
    assert api.secret_key == "test_secret_key"
    assert api.base_url == "https://api.binance.com"
    assert api.websocket_base_url == "wss://stream.binance.com:9443/ws"

def test_binance_api_init_futures():
    api = BinanceAPI(futures=True)
    assert api.base_url == "https://fapi.binance.com"
    assert api.websocket_base_url == "wss://fstream.binance.com/ws"

# Test _sign_request method
def test_sign_request():
    api = BinanceAPI()
    params = {"symbol": "BTCUSDT", "limit": 10}
    signed_params = api._sign_request(params.copy())
    assert "signature" in signed_params
    assert signed_params["symbol"] == "BTCUSDT"

# Test _send_request with mocked requests
@patch("requests.Session.get")
def test_send_request_success(mock_get):
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"code": 200, "data": "test_data"}
    mock_get.return_value = mock_response

    api = BinanceAPI()
    result = api._send_request("GET", "/test_path")
    assert result == {"code": 200, "data": "test_data"}
    mock_get.assert_called_once()

@patch("requests.Session.get")
def test_send_request_http_error(mock_get):
    mock_response = MagicMock()
    mock_response.status_code = 400
    mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("Bad Request")
    mock_response.text = "Error message"
    mock_get.return_value = mock_response

    api = BinanceAPI()
    result = api._send_request("GET", "/test_path")
    assert result is None

@patch("requests.Session.get")
def test_send_request_connection_error(mock_get):
    mock_get.side_effect = requests.exceptions.ConnectionError("No connection")

    api = BinanceAPI()
    result = api._send_request("GET", "/test_path")
    assert result is None

# Test get_klines
@patch("data_collection.binance_api.BinanceAPI._send_request")
def test_get_klines(mock_send_request):
    mock_send_request.return_value = [["kline_data"]]
    api = BinanceAPI()
    klines = api.get_klines("BTCUSDT", "1m")
    assert klines == [["kline_data"]]
    mock_send_request.assert_called_with("GET", "/api/v3/klines", params={
        "symbol": "BTCUSDT",
        "interval": "1m",
        "limit": 500
    })

# Test WebSocket connection (mocking websocket-client)
@patch("websocket.WebSocketApp")
@patch("threading.Thread")
def test_connect_websocket(mock_thread, mock_websocket_app):
    api = BinanceAPI()
    api.connect_websocket("btcusdt@kline_1m")
    mock_websocket_app.assert_called_once()
    mock_thread.assert_called_once()
    mock_thread.return_value.start.assert_called_once()
    assert api.ws_connected == False # Initially false, set to true in _on_open callback

@patch("websocket.WebSocketApp")
def test_disconnect_websocket(mock_websocket_app):
    api = BinanceAPI()
    api.ws_app = mock_websocket_app.return_value
    api.ws_thread = MagicMock()
    api.disconnect_websocket()
    api.ws_app.close.assert_called_once()
    api.ws_thread.join.assert_called_once()



