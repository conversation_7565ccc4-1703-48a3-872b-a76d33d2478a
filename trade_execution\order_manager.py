
import os
from dotenv import load_dotenv
from data_collection.binance_api import BinanceAPI

load_dotenv()

# Initialize BinanceAPI for Futures trading
# Ensure BINANCE_API_KEY and BINANCE_SECRET_KEY are set in your .env file
binance_futures_api = BinanceAPI(futures=True)

def set_leverage(symbol: str, leverage: int = 10):
    try:
        # Binance API for futures has a specific endpoint for changing leverage
        # This might need to be added to BinanceAPI class if not already there
        # For now, we'll assume a direct call or a method in BinanceAPI handles this.
        # If BinanceAPI doesn't have change_leverage, you'd need to add it.
        # Example: binance_futures_api._send_request("POST", "/fapi/v1/leverage", params={\'symbol\': symbol, \'leverage\': leverage}, signed=True)
        
        # For simplicity, let's assume BinanceAPI has a method for this or we call directly if needed
        # If using python-binance client directly for this specific call:
        # from binance.um_futures import UMFutures
        # client = UMFutures(key=os.getenv("BINANCE_API_KEY"), secret=os.getenv("BINANCE_SECRET_KEY"))
        # res = client.change_leverage(symbol=symbol, leverage=leverage)
        
        # Placeholder: In a real scenario, you'd extend BinanceAPI with this method
        # For now, we'll simulate or use a direct call if absolutely necessary and not in BinanceAPI
        print(f"[ORDER] Simulating set leverage {leverage}x for {symbol}")
        # A more robust solution would be to add this to BinanceAPI
        # For example, add a method like: 
        # def change_leverage(self, symbol, leverage):
        #    path = 
        #    params = {
        #        \'symbol\': symbol,
        #        \'leverage\': leverage
        #    }
        #    return self._send_request("POST", path, params=params, signed=True)
        
        # Then call: binance_futures_api.change_leverage(symbol, leverage)
        
    except Exception as e:
        print(f"[ORDER] Lỗi set leverage: {e}")

def open_position(symbol: str, side: str, quantity: float, entry_price: float = None, sl: float = None, tp: float = None, leverage: int = 10):
    set_leverage(symbol, leverage)
    
    order_type = "MARKET"
    params = {
        'symbol': symbol,
        'side': side,
        'type': order_type,
        'quantity': quantity
    }
    if entry_price: # For LIMIT orders
        params['price'] = entry_price
        params['type'] = "LIMIT"
        params['timeInForce'] = "GTC" # Good Till Cancelled

    try:
        order = binance_futures_api.place_order(**params)
        if order:
            print(f"[ORDER] Open {side} {symbol} Qty={quantity} x{leverage}: {order}")
            # Đặt SL/TP nếu lệnh gốc thành công
            if sl:
                try:
                    sl_side = "SELL" if side == "BUY" else "BUY"
                    sl_order = binance_futures_api.place_order(
                        symbol=symbol,
                        side=sl_side,
                        type="STOP_MARKET",
                        stopPrice=sl,
                        closePosition="true" # For Futures API, this is a string "true"
                    )
                    if sl_order: print(f"[ORDER] Đặt SL cho {symbol}: {sl_order}")
                except Exception as e:
                    print(f"[ORDER] Lỗi đặt SL: {e}")
            if tp:
                try:
                    tp_side = "SELL" if side == "BUY" else "BUY"
                    tp_order = binance_futures_api.place_order(
                        symbol=symbol,
                        side=tp_side,
                        type="TAKE_PROFIT_MARKET",
                        stopPrice=tp,
                        closePosition="true" # For Futures API, this is a string "true"
                    )
                    if tp_order: print(f"[ORDER] Đặt TP cho {symbol}: {tp_order}")
                except Exception as e:
                    print(f"[ORDER] Lỗi đặt TP: {e}")
        else:
            print(f"[ORDER] Không thể mở vị thế {side} {symbol}. Lệnh không thành công.")
    except Exception as e:
        print(f"[ORDER] Lỗi open {side}: {e}")

def close_position(symbol: str, side: str):
    try:
        # Need to get current position information using BinanceAPI
        # Assuming BinanceAPI has a method like get_position_information
        # If not, you'd need to add it to binance_api.py
        position_info = binance_futures_api.get_account_info() # get_account_info might contain position info for futures
        
        qty = 0.0
        if position_info and 'positions' in position_info:
            for pos in position_info['positions']:
                if pos['symbol'] == symbol:
                    qty = abs(float(pos['positionAmt']))
                    break
        
        if qty > 0:
            close_side = "SELL" if side == "BUY" else "BUY" # Close BUY with SELL, Close SELL with BUY
            order = binance_futures_api.place_order(
                symbol=symbol,
                side=close_side,
                type="MARKET",
                quantity=qty,
                reduceOnly="true" # For Futures API, this is a string "true"
            )
            if order:
                print(f"[ORDER] Đóng vị thế {symbol}: {order}")
            else:
                print(f"[ORDER] Không thể đóng vị thế {symbol}. Lệnh không thành công.")
        else:
            print(f"[ORDER] Không có vị thế nào để đóng với {symbol}")
    except Exception as e:
        print(f"[ORDER] Lỗi đóng vị thế: {e}")



