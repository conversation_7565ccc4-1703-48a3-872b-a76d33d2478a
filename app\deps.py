
from typing import Generator
from db.session import get_database
from data_collection.binance_api import BinanceAPI

def get_db():
    db = get_database()
    try:
        yield db
    finally:
        # No explicit close needed for MongoDB client if it's managed by get_database
        pass

def get_binance_spot_api() -> BinanceAPI:
    return BinanceAPI(futures=False)

def get_binance_futures_api() -> BinanceAPI:
    return BinanceAPI(futures=True)


