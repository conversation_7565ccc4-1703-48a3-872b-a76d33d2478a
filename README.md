<PERSON><PERSON><PERSON> năng <PERSON>

•
<PERSON>ân tích WHALE: <PERSON> dõ<PERSON> và phân tích động thái của các "cá voi" trên thị trường.

•
<PERSON>ân tích Xu hướng Thị trường: S<PERSON> dụng các mô hình AI để dự đoán giá từ dữ liệu lịch sử.

•
Tín hiệu Mua/Bán: Đ<PERSON>a ra các tín hiệu giao dịch dựa trên phân tích kỹ thuật AI và cảm xúc thị trường.

•
Giao dịch Hoàn toàn Tự động: Tự động thực hiện các lệnh mua/bán trên Binance thông qua API.

•
Quản lý Rủi ro Cá nhân hóa: <PERSON><PERSON> gồm các chiến lược như DCA (Dollar-Cost Averaging), SL (Stop-Loss), TP (Take-Profit) l<PERSON><PERSON> ho<PERSON>, chia lệnh và kiểm soát rủi ro theo chiến lư<PERSON> riê<PERSON>.

•
<PERSON><PERSON><PERSON> giá <PERSON>ảm xúc Thị trường: <PERSON>ân tích tâm lý thị trường từ các nguồn dữ liệu khác nhau.

•
Phản ứng với Tin tức Thế giới: Khả năng tích hợp và phản ứng với các sự kiện tin tức có ảnh hưởng đến thị trường (cần phát triển thêm).

•
Tối ưu hóa Hiệu suất Futures: Các cơ chế quản lý đòn bẩy, xem xét funding rate, giảm thiểu slippage và quản lý vị thế nâng cao.

Công nghệ Sử dụng

•
Backend Framework: FastAPI

•
Cơ sở dữ liệu: MongoDB (với pymongo)

•
Thư viện Binance API: python-binance

•
Machine Learning: Keras (TensorFlow) cho các mô hình LSTM, scikit-learn

•
Xử lý Dữ liệu: Pandas, NumPy

•
Phân tích Kỹ thuật: pandas_ta

•
Quản lý Môi trường: python-dotenv

•
WebSocket: websocket-client

Yêu cầu Hệ thống

•
Python 3.9+

•
pip (trình quản lý gói của Python)

•
MongoDB (đang chạy trên localhost hoặc một URI có thể truy cập được)

Cài đặt

Thực hiện theo các bước dưới đây để cài đặt và chạy dự án trên môi trường local của bạn.

1. Clone Repository

Bash


git clone <URL_TO_YOUR_REPOSITORY>
cd autobot


2. Tạo và Kích hoạt Môi trường Ảo (Virtual Environment)

Bạn nên sử dụng môi trường ảo để quản lý các thư viện phụ thuộc của dự án.

Bash


python3 -m venv venv
source venv/bin/activate  # Trên Linux/macOS
# venv\Scripts\activate  # Trên Windows


3. Cài đặt các Thư viện Phụ thuộc

Bash


pip install -r requirements.txt


4. Cấu hình Môi trường (.env file)

Tạo một file .env trong thư mục gốc của dự án (autobot/) và điền các thông tin sau:

Plain Text


BINANCE_API_KEY=YOUR_BINANCE_API_KEY
BINANCE_SECRET_KEY=YOUR_BINANCE_SECRET_KEY
MONGO_URI=mongodb://localhost:27017/  # Hoặc URI MongoDB của bạn
MONGO_DB_NAME=binance_trading_bot


Lưu ý quan trọng:

•
Thay thế YOUR_BINANCE_API_KEY và YOUR_BINANCE_SECRET_KEY bằng API Key và Secret Key thực tế của tài khoản Binance của bạn. Đảm bảo rằng các khóa này có quyền truy cập cần thiết (ví dụ: đọc dữ liệu thị trường, giao dịch).

•
Không chia sẻ file .env này lên các hệ thống kiểm soát phiên bản công khai (ví dụ: GitHub).

•
Đảm bảo MongoDB của bạn đang chạy và có thể truy cập được từ MONGO_URI đã cấu hình.

Chạy Backend

Sau khi cài đặt và cấu hình, bạn có thể chạy ứng dụng backend FastAPI:

Bash


uvicorn app.main:app --reload --host 0.0.0.0 --port 8000


•
app.main:app: Chỉ định rằng ứng dụng FastAPI nằm trong file main.py của thư mục app và tên biến ứng dụng là app.

•
--reload: Tự động tải lại ứng dụng khi có thay đổi trong code (hữu ích cho phát triển).

•
--host 0.0.0.0: Cho phép truy cập từ mọi địa chỉ IP (cần thiết nếu bạn muốn truy cập từ máy khác trong mạng local).

•
--port 8000: Chạy ứng dụng trên cổng 8000.

Sau khi chạy thành công, bạn có thể truy cập API tại http://localhost:8000 hoặc http://127.0.0.1:8000.

Bạn có thể kiểm tra endpoint gốc:
http://localhost:8000/

Hoặc truy cập tài liệu API tương tác (Swagger UI):
http://localhost:8000/docs

Cấu trúc Dự án

Plain Text


autobot/
├── app/                      # Các thành phần chính của ứng dụng FastAPI
│   ├── __init__.py
│   ├── main.py               # Điểm khởi đầu của ứng dụng FastAPI
│   ├── api_router.py         # Định nghĩa các API routes
│   └── deps.py               # Dependency injection cho FastAPI (DB, API clients)
├── config.py                 # Cấu hình chung của ứng dụng
├── core/                     # Cấu hình cốt lõi, bảo mật, cài đặt
│   ├── config.py             # Cấu hình API keys, DB URI
│   ├── security.py           # Xử lý bảo mật (hashing password)
│   └── settings.py           # Các cài đặt ứng dụng
├── data_analysis/            # Các module phân tích dữ liệu
│   ├── __init__.py
│   ├── backtest.py           # Module backtesting
│   ├── lstm_multi_feature.keras # Mô hình LSTM đã huấn luyện (multi-feature)
│   ├── lstm_saved_model.keras   # Mô hình LSTM đã huấn luyện (single-feature)
│   ├── preprocessing.py      # Tiền xử lý dữ liệu
│   ├── price_prediction.py   # Dự đoán giá
│   ├── sentiment_analysis.py # Phân tích cảm xúc thị trường
│   ├── technical_analysis.py # Phân tích kỹ thuật
│   └── whale_analysis.py     # Phân tích cá voi
├── data_collection/          # Thu thập và lưu trữ dữ liệu
│   ├── __init__.py
│   ├── binance_api.py        # Tương tác với Binance API (REST & WebSocket)
│   └── data_storage.py       # Lưu trữ dữ liệu vào DB
├── db/                       # Tương tác với cơ sở dữ liệu
│   ├── __init__.py
│   ├── crud.py               # Các thao tác CRUD cơ bản với MongoDB
│   ├── session.py            # Quản lý phiên kết nối DB
│   └── trade_log.py          # Ghi nhật ký giao dịch
├── logging_monitoring/       # Ghi nhật ký và giám sát
│   ├── __init__.py
│   └── logger.py             # Cấu hình hệ thống ghi nhật ký
├── models/                   # Định nghĩa các Pydantic models cho dữ liệu
│   ├── __init__.py
│   ├── candle.py             # Model cho dữ liệu nến (kline)
│   ├── trade.py              # Model cho dữ liệu giao dịch
│   └── user.py               # Model cho người dùng
├── risk_management/          # Quản lý rủi ro
│   ├── __init__.py
│   └── risk_rules.py         # Các quy tắc quản lý rủi ro (SL/TP, DCA, position sizing)
├── scheduler/                # Lập lịch các tác vụ tự động
│   ├── ai_pipeline.py        # Pipeline AI tự động
│   ├── train_lstm_multi_scheduler.py # Lập lịch huấn luyện LSTM multi-feature
│   └── train_lstm_scheduler.py     # Lập lịch huấn luyện LSTM single-feature
├── strategy_management/      # Quản lý các chiến lược giao dịch
│   ├── __init__.py
│   └── strategies.py         # Định nghĩa các chiến lược giao dịch
├── tests/                    # Các bài kiểm thử
│   ├── __init__.py
│   ├── test_ai_pipeline.py
│   ├── test_ai_pipeline_realtime.py
│   ├── test_analysis.py
│   ├── test_api.py
│   └── test_strategy.py
├── trade_execution/          # Thực thi giao dịch
│   ├── __init__.py
│   └── order_manager.py      # Quản lý và thực thi lệnh
├── utils/                    # Các hàm tiện ích chung
├── .env.example              # File mẫu cho cấu hình môi trường
├── create_binance_structure.sh # Script tạo cấu trúc thư mục Binance (nếu cần)
├── README.md                 # File hướng dẫn này
└── requirements.txt          # Danh sách các thư viện phụ thuộc


Chạy Tests

Để chạy các bài kiểm thử của dự án, đảm bảo bạn đã cài đặt pytest (có trong requirements.txt).


pytest
