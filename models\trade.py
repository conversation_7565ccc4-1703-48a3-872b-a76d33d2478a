
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class Trade(BaseModel):
    symbol: str
    order_id: str
    client_order_id: str
    price: float
    quantity: float
    executed_qty: float
    cum_quote_qty: float
    status: str
    time_in_force: str
    type: str
    side: str
    stop_price: Optional[float] = None
    iceberg_qty: Optional[float] = None
    time: datetime
    update_time: datetime
    is_working: bool
    orig_qty: float

    class Config:
        json_encoders = {
            datetime: lambda v: v.timestamp() * 1000, # Convert datetime to milliseconds timestamp
        }
        # allow_population_by_field_name = True
        # arbitrary_types_allowed = True



