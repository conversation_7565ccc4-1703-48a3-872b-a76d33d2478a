
import pandas as pd
from data_collection.binance_api import BinanceAPI
from db.session import get_database

class DataStorage:
    def __init__(self, futures=False):
        self.binance_api = BinanceAPI(futures=futures)
        self.db = get_database() # Assuming MongoDB connection

    def fetch_and_store_klines(self, symbol, interval, limit=500):
        print(f"Fetching {symbol} {interval} klines...")
        klines = self.binance_api.get_klines(symbol, interval, limit)
        if klines:
            # Convert klines to DataFrame for easier processing
            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            df["open_time"] = pd.to_datetime(df["open_time"], unit="ms")
            df["close_time"] = pd.to_datetime(df["close_time"], unit="ms")
            
            # Store in MongoDB (example)
            collection_name = f"klines_{symbol}_{interval}"
            collection = self.db[collection_name]
            
            # Convert DataFrame to list of dictionaries for MongoDB insertion
            records = df.to_dict(orient=\



                'records')
            
            try:
                collection.insert_many(records)
                print(f"Successfully stored {len(records)} klines for {symbol}-{interval} in MongoDB.")
            except Exception as e:
                print(f"Error storing klines in MongoDB: {e}")
        else:
            print(f"No klines fetched for {symbol}-{interval}.")

    def get_klines_from_db(self, symbol, interval, start_time=None, end_time=None):
        collection_name = f"klines_{symbol}_{interval}"
        collection = self.db[collection_name]
        query = {}
        if start_time:
            query["open_time"] = {"$gte": start_time}
        if end_time:
            query["close_time"] = {"$lte": end_time}
        
        data = list(collection.find(query).sort("open_time", 1))
        if data:
            df = pd.DataFrame(data)
            # Convert relevant columns to numeric types if they are not already
            numeric_cols = ["open", "high", "low", "close", "volume", "quote_asset_volume", "number_of_trades", "taker_buy_base_asset_volume", "taker_buy_quote_asset_volume"]
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors=\'coerce\')
            return df
        return pd.DataFrame()


# Example Usage (for testing purposes)
if __name__ == '__main__':
    # Ensure your .env has BINANCE_API_KEY, BINANCE_SECRET_KEY, and MONGO_URI
    # And your MongoDB is running

    # Spot Data Storage Example
    spot_data_storage = DataStorage(futures=False)
    print("\n--- Spot Data Storage Test ---")
    spot_data_storage.fetch_and_store_klines("BTCUSDT", "1m", limit=10)

    # Futures Data Storage Example
    futures_data_storage = DataStorage(futures=True)
    print("\n--- Futures Data Storage Test ---")
    futures_data_storage.fetch_and_store_klines("ETHUSDT", "5m", limit=10)

    # Get data from DB example
    df_spot = spot_data_storage.get_klines_from_db("BTCUSDT", "1m")
    if not df_spot.empty:
        print("\nFetched BTCUSDT 1m klines from DB (Spot):")
        print(df_spot.head())

    df_futures = futures_data_storage.get_klines_from_db("ETHUSDT", "5m")
    if not df_futures.empty:
        print("\nFetched ETHUSDT 5m klines from DB (Futures):")
        print(df_futures.head())



