
import os
import time
import hmac
import hashlib
import requests
import json
import websocket
import threading
from urllib.parse import urlencode
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configuration for Binance API
API_KEY = os.getenv('BINANCE_API_KEY')
SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')
BASE_URL = 'https://api.binance.com'
FUTURES_BASE_URL = 'https://fapi.binance.com'
WEBSOCKET_BASE_URL = 'wss://stream.binance.com:9443/ws'
FUTURES_WEBSOCKET_BASE_URL = 'wss://fstream.binance.com/ws'

# Simple Rate Limiter (for demonstration purposes)
# Binance has complex rate limits, a more robust solution might be needed for production
LAST_REQUEST_TIME = 0
REQUEST_INTERVAL = 0.1  # seconds, adjust based on Binance's rate limits

def enforce_rate_limit():
    global LAST_REQUEST_TIME
    current_time = time.time()
    elapsed_time = current_time - LAST_REQUEST_TIME
    if elapsed_time < REQUEST_INTERVAL:
        time.sleep(REQUEST_INTERVAL - elapsed_time)
    LAST_REQUEST_TIME = time.time()

class BinanceAPI:
    def __init__(self, api_key=API_KEY, secret_key=SECRET_KEY, futures=False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = FUTURES_BASE_URL if futures else BASE_URL
        self.websocket_base_url = FUTURES_WEBSOCKET_BASE_URL if futures else WEBSOCKET_BASE_URL
        self.futures = futures
        self.session = requests.Session()
        self.session.headers.update({'X-MBX-APIKEY': self.api_key})

        self.ws_app = None
        self.ws_thread = None
        self.ws_connected = False
        self.ws_reconnect_interval = 5 # seconds

    def _sign_request(self, params):
        query_string = urlencode(params)
        signature = hmac.new(self.secret_key.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
        params['signature'] = signature
        return params

    def _send_request(self, method, path, params=None, signed=False):
        enforce_rate_limit()
        url = f"{self.base_url}{path}"
        if params is None:
            params = {}

        if signed:
            params['timestamp'] = int(time.time() * 1000)
            params = self._sign_request(params)

        try:
            if method == 'GET':
                response = self.session.get(url, params=params)
            elif method == 'POST':
                response = self.session.post(url, data=params)
            elif method == 'PUT':
                response = self.session.put(url, data=params)
            elif method == 'DELETE':
                response = self.session.delete(url, params=params)
            else:
                raise ValueError("Unsupported HTTP method")

            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            # Log the error, potentially notify user, or implement retry logic
            return None
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            # Log the error, potentially notify user, or implement retry logic
            return None
        except requests.exceptions.Timeout as timeout_err:
            print(f"Timeout error occurred: {timeout_err}")
            # Log the error, potentially notify user, or implement retry logic
            return None
        except requests.exceptions.RequestException as req_err:
            print(f"An unexpected error occurred: {req_err}")
            # Log the error, potentially notify user, or implement retry logic
            return None

    # Public API methods (examples)
    def get_exchange_info(self):
        return self._send_request('GET', '/api/v3/exchangeInfo')

    def get_klines(self, symbol, interval, limit=500):
        path = '/api/v3/klines' if not self.futures else '/fapi/v1/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        return self._send_request('GET', path, params=params)

    def get_account_info(self):
        path = '/api/v3/account' if not self.futures else '/fapi/v2/account'
        return self._send_request('GET', path, signed=True)

    def place_order(self, symbol, side, type, quantity, price=None, timeInForce=None):
        path = '/api/v3/order' if not self.futures else '/fapi/v1/order'
        params = {
            'symbol': symbol,
            'side': side,
            'type': type,
            'quantity': quantity
        }
        if price: params['price'] = price
        if timeInForce: params['timeInForce'] = timeInForce
        return self._send_request('POST', path, params=params, signed=True)

    # WebSocket Management
    def _on_open(self, ws):
        print("WebSocket connection opened.")
        self.ws_connected = True

    def _on_message(self, ws, message):
        # print(f"Received: {message}")
        # This is where you would process incoming WebSocket data
        # e.g., update market data, account info, etc.
        pass

    def _on_error(self, ws, error):
        print(f"WebSocket error: {error}")

    def _on_close(self, ws, close_status_code, close_msg):
        print(f"WebSocket connection closed: {close_status_code} - {close_msg}")
        self.ws_connected = False
        # Attempt to reconnect after a delay
        print(f"Attempting to reconnect WebSocket in {self.ws_reconnect_interval} seconds...")
        time.sleep(self.ws_reconnect_interval)
        self.connect_websocket(self.stream_name) # Reconnect to the same stream

    def connect_websocket(self, stream_name, on_message_callback=None):
        if self.ws_connected:
            print("WebSocket is already connected.")
            return

        self.stream_name = stream_name
        full_ws_url = f"{self.websocket_base_url}/{stream_name}"

        if on_message_callback:
            self._on_message = on_message_callback # Allow custom message handling

        self.ws_app = websocket.WebSocketApp(
            full_ws_url,
            on_open=self._on_open,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close
        )

        self.ws_thread = threading.Thread(target=self.ws_app.run_forever, daemon=True)
        self.ws_thread.start()
        print(f"Attempting to connect to WebSocket: {full_ws_url}")

    def disconnect_websocket(self):
        if self.ws_app:
            self.ws_app.close()
            self.ws_thread.join(timeout=5) # Wait for thread to finish
            print("WebSocket disconnected.")

# Example Usage (for testing purposes, remove in production main.py)
if __name__ == '__main__':
    # Ensure you have BINANCE_API_KEY and BINANCE_SECRET_KEY in your .env file
    # For testing, you might use a testnet API key/secret

    # Spot API Example
    spot_api = BinanceAPI()
    print("\n--- Spot API Test ---")
    # info = spot_api.get_exchange_info()
    # if info: print(f"Exchange Info (Spot): {info['symbols'][0]['symbol']}")

    # klines = spot_api.get_klines('BTCUSDT', '1m', limit=5)
    # if klines: print(f"Klines (Spot): {klines}")

    # account = spot_api.get_account_info()
    # if account: print(f"Account Info (Spot): {account['balances'][0]}")

    # Futures API Example
    futures_api = BinanceAPI(futures=True)
    print("\n--- Futures API Test ---")
    # futures_klines = futures_api.get_klines('BTCUSDT', '1m', limit=5)
    # if futures_klines: print(f"Klines (Futures): {futures_klines}")

    # futures_account = futures_api.get_account_info()
    # if futures_account: print(f"Account Info (Futures): {futures_account['assets'][0]}")

    # WebSocket Example (Spot - BTCUSDT kline 1m)
    print("\n--- WebSocket Test (Spot) ---")
    def custom_spot_message_handler(ws, message):
        json_message = json.loads(message)
        # print(f"Custom Spot Message: {json_message}")
        if 'k' in json_message:
            kline_data = json_message['k']
            print(f"Spot Kline: {kline_data['s']} - Open: {kline_data['o']}, Close: {kline_data['c']}")

    spot_api.connect_websocket('btcusdt@kline_1m', on_message_callback=custom_spot_message_handler)

    # WebSocket Example (Futures - BTCUSDT kline 1m)
    print("\n--- WebSocket Test (Futures) ---")
    def custom_futures_message_handler(ws, message):
        json_message = json.loads(message)
        # print(f"Custom Futures Message: {json_message}")
        if 'k' in json_message:
            kline_data = json_message['k']
            print(f"Futures Kline: {kline_data['s']} - Open: {kline_data['o']}, Close: {kline_data['c']}")

    futures_api.connect_websocket('btcusdt@kline_1m', on_message_callback=custom_futures_message_handler)

    try:
        while True:
            time.sleep(1) # Keep main thread alive to allow WebSocket threads to run
    except KeyboardInterrupt:
        print("\nDisconnecting WebSockets...")
        spot_api.disconnect_websocket()
        futures_api.disconnect_websocket()
        print("Exiting.")



