#!/usr/bin/env python3
"""
Test Real-time Demo Script
Chạy bot với dữ liệu real-time nhưng không thực hiện lệnh giao dịch thật.
Chỉ log và hiển thị tín hiệu, phân tích.
"""

import asyncio
import time
import requests
import pandas as pd
from datetime import datetime
import os
from dotenv import load_dotenv

# Import các module của bot
from data_collection.binance_api import BinanceDataCollector
from data_analysis.technical_analysis import TechnicalAnalyzer
from data_analysis.price_prediction import load_and_predict_lstm_multi
from strategy_management.strategies import SimpleMovingAverageStrategy
from risk_management.risk_rules import calculate_stop_loss_take_profit, calculate_position_size
from db.session import get_database
from db.trade_log import log_trade_signal

load_dotenv()

class RealTimeTestBot:
    def __init__(self):
        self.test_mode = True
        self.symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]  # Top 3 symbols for testing
        self.interval = "5m"
        self.db = get_database()
        self.data_collector = BinanceDataCollector()
        self.technical_analyzer = TechnicalAnalyzer()
        self.strategy = SimpleMovingAverageStrategy()
        
        # Demo account balance
        self.demo_balance = 10000.0  # $10,000 demo balance
        self.demo_positions = {}
        
        print("🚀 Real-time Test Bot initialized")
        print(f"📊 Monitoring symbols: {self.symbols}")
        print(f"⏰ Interval: {self.interval}")
        print(f"💰 Demo balance: ${self.demo_balance}")
        print("🔒 TEST MODE: No real trades will be executed")
        print("-" * 60)

    def get_latest_price(self, symbol):
        """Lấy giá real-time từ Binance"""
        try:
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])
        except Exception as e:
            print(f"❌ Error getting price for {symbol}: {e}")
        return None

    def collect_and_analyze_data(self, symbol):
        """Thu thập và phân tích dữ liệu cho một symbol"""
        try:
            # Lấy dữ liệu kline từ Binance
            url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={self.interval}&limit=100"
            response = requests.get(url)
            
            if response.status_code != 200:
                print(f"❌ Failed to get kline data for {symbol}")
                return None
                
            klines = response.json()
            
            # Chuyển đổi thành DataFrame
            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
            
            # Tính các chỉ báo kỹ thuật
            df = self.technical_analyzer.add_all_indicators(df)
            
            return df
            
        except Exception as e:
            print(f"❌ Error collecting data for {symbol}: {e}")
            return None

    def generate_signal(self, symbol, df):
        """Tạo tín hiệu giao dịch"""
        try:
            if df is None or len(df) < 50:
                return None
                
            # Lấy dữ liệu mới nhất
            latest = df.iloc[-1]
            prev = df.iloc[-2]
            
            # Sử dụng strategy để tạo signal
            signal_data = self.strategy.generate_signal(df)
            
            if signal_data and signal_data.get('signal') != 'HOLD':
                # Tính SL/TP
                sl_tp = calculate_stop_loss_take_profit(
                    latest['close'], 
                    signal_data['signal']
                )
                
                # Tính position size
                position_size = calculate_position_size(
                    balance=self.demo_balance,
                    risk_pct=1.0,  # 1% risk per trade
                    sl_price=sl_tp['stop_loss'] if sl_tp else None,
                    symbol_price=latest['close']
                )
                
                return {
                    'symbol': symbol,
                    'signal': signal_data['signal'],
                    'price': latest['close'],
                    'rsi': latest.get('rsi'),
                    'ma_20': latest.get('ma_20'),
                    'ma_50': latest.get('ma_50'),
                    'stop_loss': sl_tp['stop_loss'] if sl_tp else None,
                    'take_profit': sl_tp['take_profit'] if sl_tp else None,
                    'position_size': position_size,
                    'confidence': signal_data.get('confidence', 0.5),
                    'timestamp': datetime.now()
                }
                
        except Exception as e:
            print(f"❌ Error generating signal for {symbol}: {e}")
        
        return None

    def log_demo_trade(self, signal):
        """Log giao dịch demo (không thực hiện giao dịch thật)"""
        try:
            print(f"\n🎯 DEMO TRADE SIGNAL:")
            print(f"   Symbol: {signal['symbol']}")
            print(f"   Signal: {signal['signal']}")
            print(f"   Price: ${signal['price']:.4f}")
            print(f"   RSI: {signal['rsi']:.2f}" if signal['rsi'] else "   RSI: N/A")
            print(f"   MA20: ${signal['ma_20']:.4f}" if signal['ma_20'] else "   MA20: N/A")
            print(f"   MA50: ${signal['ma_50']:.4f}" if signal['ma_50'] else "   MA50: N/A")
            print(f"   Stop Loss: ${signal['stop_loss']:.4f}" if signal['stop_loss'] else "   Stop Loss: N/A")
            print(f"   Take Profit: ${signal['take_profit']:.4f}" if signal['take_profit'] else "   Take Profit: N/A")
            print(f"   Position Size: {signal['position_size']:.6f}")
            print(f"   Confidence: {signal['confidence']:.2%}")
            print(f"   Time: {signal['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Lưu vào database để tracking
            log_trade_signal(
                symbol=signal['symbol'],
                interval=self.interval,
                signal=signal['signal'],
                last_close=signal['price'],
                prediction=signal['price'],  # Placeholder
                stop_loss=signal['stop_loss'] or 0,
                take_profit=signal['take_profit'] or 0,
                position_size=signal['position_size']
            )
            
            print("   ✅ Signal logged to database")
            
        except Exception as e:
            print(f"❌ Error logging demo trade: {e}")

    async def run_cycle(self):
        """Chạy một chu kỳ phân tích cho tất cả symbols"""
        print(f"\n🔄 Running analysis cycle at {datetime.now().strftime('%H:%M:%S')}")
        
        for symbol in self.symbols:
            try:
                # Lấy giá hiện tại
                current_price = self.get_latest_price(symbol)
                if current_price:
                    print(f"📈 {symbol}: ${current_price:.4f}")
                
                # Thu thập và phân tích dữ liệu
                df = self.collect_and_analyze_data(symbol)
                
                # Tạo tín hiệu
                signal = self.generate_signal(symbol, df)
                
                if signal:
                    self.log_demo_trade(signal)
                else:
                    print(f"   📊 {symbol}: No signal (HOLD)")
                    
            except Exception as e:
                print(f"❌ Error processing {symbol}: {e}")
        
        print("-" * 60)

    async def run(self):
        """Chạy bot liên tục"""
        print("🚀 Starting Real-time Test Bot...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                await self.run_cycle()
                
                # Đợi 5 phút (300 giây) cho chu kỳ tiếp theo
                print(f"⏳ Waiting 5 minutes for next cycle...")
                await asyncio.sleep(300)
                
        except KeyboardInterrupt:
            print("\n🛑 Bot stopped by user")
        except Exception as e:
            print(f"❌ Bot error: {e}")

def main():
    """Main function"""
    print("=" * 60)
    print("🤖 BINANCE TRADING BOT - REAL-TIME TEST MODE")
    print("=" * 60)
    
    bot = RealTimeTestBot()
    
    # Chạy bot
    asyncio.run(bot.run())

if __name__ == "__main__":
    main()
