#!/usr/bin/env python3
"""
Test API Endpoints - Test các API endpoints của bot
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, method="GET", params=None, data=None):
    """Test một API endpoint"""
    try:
        url = f"{BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, params=params)
        elif method == "POST":
            response = requests.post(url, params=params, json=data)
        
        print(f"🔗 {method} {endpoint}")
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success")
            return result
        else:
            print(f"❌ Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def main():
    """Test các API endpoints"""
    print("=" * 60)
    print("🧪 TESTING API ENDPOINTS")
    print("=" * 60)
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Base URL: {BASE_URL}")
    print()
    
    # Test 1: Health check
    print("1️⃣ Testing Health Check...")
    result = test_endpoint("/")
    if result:
        print(f"   Message: {result.get('msg')}")
    print()
    
    # Test 2: MongoDB connection
    print("2️⃣ Testing MongoDB Connection...")
    result = test_endpoint("/mongo-test")
    if result:
        print(f"   Collections: {result.get('collections')}")
    print()
    
    # Test 3: Get current price
    print("3️⃣ Testing Current Price...")
    result = test_endpoint("/price", params={"symbol": "BTCUSDT"})
    if result:
        print(f"   {result.get('symbol')}: ${result.get('price')}")
    print()
    
    # Test 4: Get multiple prices
    print("4️⃣ Testing Multiple Prices...")
    result = test_endpoint("/prices", params={"symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"]})
    if result and result.get('data'):
        for item in result['data']:
            if item.get('success'):
                print(f"   {item.get('symbol')}: ${item.get('price')}")
            else:
                print(f"   {item.get('symbol')}: Error")
    print()
    
    # Test 5: Get kline data
    print("5️⃣ Testing Kline Data...")
    result = test_endpoint("/kline", params={
        "symbol": "BTCUSDT",
        "interval": "5m",
        "limit": 10,
        "save": False
    })
    if result and result.get('data'):
        print(f"   Symbol: {result.get('symbol')}")
        print(f"   Interval: {result.get('interval')}")
        print(f"   Data points: {len(result['data'])}")
        if result['data']:
            latest = result['data'][-1]
            print(f"   Latest close: ${latest.get('close')}")
    print()
    
    # Test 6: Get kline data from DB
    print("6️⃣ Testing Kline Data from DB...")
    result = test_endpoint("/kline-db", params={
        "symbol": "BTCUSDT",
        "interval": "5m",
        "limit": 10
    })
    if result and result.get('data'):
        print(f"   Symbol: {result.get('symbol')}")
        print(f"   Data points from DB: {len(result['data'])}")
        if result['data']:
            latest = result['data'][-1]
            print(f"   Latest close from DB: ${latest.get('close')}")
    print()
    
    # Test 7: Calculate indicators
    print("7️⃣ Testing Technical Indicators...")
    result = test_endpoint("/indicator", params={
        "symbol": "BTCUSDT",
        "interval": "5m",
        "limit": 50
    })
    if result and result.get('data'):
        print(f"   Symbol: {result.get('symbol')}")
        print(f"   Data points: {len(result['data'])}")
        if result['data']:
            latest = result['data'][-1]
            print(f"   Latest RSI: {latest.get('rsi'):.2f}" if latest.get('rsi') else "   Latest RSI: N/A")
            print(f"   Latest MA: ${latest.get('ma'):.2f}" if latest.get('ma') else "   Latest MA: N/A")
            print(f"   Latest MACD: {latest.get('macd'):.6f}" if latest.get('macd') else "   Latest MACD: N/A")
    print()
    
    # Test 8: Get trade signals
    print("8️⃣ Testing Trade Signals...")
    result = test_endpoint("/trade-signals", params={
        "symbol": "BTCUSDT",
        "limit": 5
    })
    if result:
        print(f"   Trade signals count: {len(result) if isinstance(result, list) else 'N/A'}")
        if isinstance(result, list) and result:
            latest_signal = result[0]
            print(f"   Latest signal: {latest_signal.get('signal')}")
            print(f"   Latest price: ${latest_signal.get('last_close')}")
    print()
    
    # Test 9: Save kline data to DB
    print("9️⃣ Testing Save Kline Data to DB...")
    result = test_endpoint("/kline", params={
        "symbol": "BTCUSDT",
        "interval": "5m",
        "limit": 20,
        "save": True
    })
    if result:
        print(f"   Symbol: {result.get('symbol')}")
        print(f"   Saved records: {result.get('saved', 0)}")
        print(f"   Data points: {len(result.get('data', []))}")
    print()
    
    # Test 10: Create a demo trade signal
    print("🔟 Testing Create Trade Signal...")
    result = test_endpoint("/trade-signal", method="POST", params={
        "symbol": "BTCUSDT",
        "interval": "5m",
        "signal": "BUY",
        "last_close": 107500.0,
        "prediction": 108000.0,
        "stop_loss": 106000.0,
        "take_profit": 109000.0,
        "position_size": 0.001
    })
    if result:
        print(f"   Signal created: {result.get('signal')}")
        print(f"   Symbol: {result.get('symbol')}")
        print(f"   Timestamp: {result.get('timestamp')}")
    print()
    
    print("=" * 60)
    print("✅ API ENDPOINTS TEST COMPLETED!")
    print("💡 All endpoints are working with real-time data")
    print("🔒 No real trades executed - TEST MODE ONLY")

if __name__ == "__main__":
    main()
