#!/usr/bin/env python3
"""
Run All Tests - Script tổng hợp để chạy tất cả các test
"""

import subprocess
import sys
import time
import requests
from datetime import datetime

def check_server_running():
    """Kiểm tra xem server có đang chạy không"""
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def run_command(command, description):
    """Chạy một command và hiển thị kết quả"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"⚡ Command: {command}")
    print(f"⏰ Time: {datetime.now().strftime('%H:%M:%S')}")
    print("-" * 60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print("📤 Output:")
                print(result.stdout)
        else:
            print("❌ FAILED")
            if result.stderr:
                print("🚨 Error:")
                print(result.stderr)
            if result.stdout:
                print("📤 Output:")
                print(result.stdout)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ TIMEOUT - Command took too long")
        return False
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
        return False

def main():
    """Main function"""
    print("🤖 BINANCE TRADING BOT - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔒 TEST MODE: No real trades will be executed")
    print("=" * 80)
    
    # Kiểm tra server
    print("\n🔍 Checking if FastAPI server is running...")
    if not check_server_running():
        print("❌ FastAPI server is not running!")
        print("💡 Please start the server first:")
        print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return
    
    print("✅ FastAPI server is running!")
    
    # Danh sách các test
    tests = [
        {
            "command": "python quick_test.py",
            "description": "Quick Real-time Analysis Test",
            "required": True
        },
        {
            "command": "python test_api_endpoints.py",
            "description": "API Endpoints Test",
            "required": True
        }
    ]
    
    # Chạy các test
    passed = 0
    failed = 0
    
    for test in tests:
        success = run_command(test["command"], test["description"])
        
        if success:
            passed += 1
        else:
            failed += 1
            if test.get("required", False):
                print(f"\n🚨 CRITICAL TEST FAILED: {test['description']}")
    
    # Tổng kết
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%" if (passed+failed) > 0 else "N/A")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Your trading bot is ready for real-time testing!")
        print("\n💡 Next steps:")
        print("   1. Run 'python realtime_monitor.py' for live dashboard")
        print("   2. Run 'python test_realtime_demo.py' for continuous monitoring")
        print("   3. Configure your Binance API keys in .env for live data")
        print("   4. Set TEST_MODE=1 to prevent real trading")
    else:
        print(f"\n⚠️  {failed} test(s) failed!")
        print("🔧 Please fix the issues before proceeding")
    
    print("\n🔒 Remember: TEST_MODE=1 ensures no real trades are executed")
    print("=" * 80)

if __name__ == "__main__":
    main()
