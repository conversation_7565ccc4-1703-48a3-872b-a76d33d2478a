thinc-8.3.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
thinc-8.3.6.dist-info/METADATA,sha256=o6ybrYU4s_Yjmz605RQgwU22ZcqDflhB1B1_QqA8BBQ,15610
thinc-8.3.6.dist-info/RECORD,,
thinc-8.3.6.dist-info/WHEEL,sha256=_ZWIY2n7n6SpiuIFl1-RvcMp4Ty36T57FKf-7NzqZHM,101
thinc-8.3.6.dist-info/entry_points.txt,sha256=XKKdq_CSXqo6JQy6KFFwvYfpyR__SkBiRQUYw_Bg9WA,66
thinc-8.3.6.dist-info/licenses/LICENSE,sha256=yRgWFaN6OZ2fO7xD0j2omgY55O0zB7NLgFmHWt6qFjU,1144
thinc-8.3.6.dist-info/top_level.txt,sha256=u3-NetjMbYinrdypKSLTjWYAhRmoOzTqbb_xXWaxW8Q,6
thinc/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/__init__.py,sha256=SpDzvQkZysG476pfp2ytV5X5YqvmMX10alepZx10mRM,225
thinc/__pycache__/__init__.cpython-311.pyc,,
thinc/__pycache__/about.cpython-311.pyc,,
thinc/__pycache__/api.cpython-311.pyc,,
thinc/__pycache__/compat.cpython-311.pyc,,
thinc/__pycache__/config.cpython-311.pyc,,
thinc/__pycache__/initializers.cpython-311.pyc,,
thinc/__pycache__/loss.cpython-311.pyc,,
thinc/__pycache__/model.cpython-311.pyc,,
thinc/__pycache__/mypy.cpython-311.pyc,,
thinc/__pycache__/optimizers.cpython-311.pyc,,
thinc/__pycache__/schedules.cpython-311.pyc,,
thinc/__pycache__/types.cpython-311.pyc,,
thinc/__pycache__/util.cpython-311.pyc,,
thinc/about.py,sha256=Q1RuvAuuqclnxdIP1O7hYWBIRPF1hDKm_WowG3B4QVY,43
thinc/api.py,sha256=ripkrdcCq4v4ddj7Q2rzitUlyNXAKe1_e2H3ZkeH2VE,6093
thinc/backends/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/backends/__init__.py,sha256=Ijexqmm7W1RzZAGgSzoC4JqHmLBzoTgbdYlXJEnoiZs,5592
thinc/backends/__pycache__/__init__.cpython-311.pyc,,
thinc/backends/__pycache__/_cupy_allocators.cpython-311.pyc,,
thinc/backends/__pycache__/_custom_kernels.cpython-311.pyc,,
thinc/backends/__pycache__/_param_server.cpython-311.pyc,,
thinc/backends/__pycache__/cupy_ops.cpython-311.pyc,,
thinc/backends/__pycache__/mps_ops.cpython-311.pyc,,
thinc/backends/__pycache__/ops.cpython-311.pyc,,
thinc/backends/_cupy_allocators.py,sha256=NF0c-24aZps8IeiSz0yINTRSsi_1Y9BdA9-qiKBYx3M,2262
thinc/backends/_custom_kernels.cu,sha256=ExeR0gPsYf42G1MufCsMKUCMMx-wo-a9O1XK41jW3k8,19480
thinc/backends/_custom_kernels.py,sha256=t4byN4HM2v9H1V7oDwKxlqx4dhfxfdSWq6_PhZ94f4I,26253
thinc/backends/_murmur3.cu,sha256=rkwVb8Np59rVIzrokrT8pz-tDUKbvDcnQH_aSn7ZqPo,5398
thinc/backends/_param_server.py,sha256=GrMS_QmjA-XSuozj-uJwZOJ3OsjdQQISjFRxHiCa-30,2819
thinc/backends/cblas.cp311-win_amd64.pyd,sha256=DpPOEx8iAEsDo-ExvX-ZphaDS8Na7UYiYy7c12jKsCo,133632
thinc/backends/cblas.cpp,sha256=73W9N0wGX1_GSZBKHzzADU8Po12jESNx43XedTkK8ng,1002279
thinc/backends/cblas.pxd,sha256=bc3Y3EkNInQxjXn8lwBnxKolSe2gGlsJkT-vDffoBAU,1475
thinc/backends/cblas.pyx,sha256=o8UxBN_pk_BLLrK6ZniTT5jSP0BkrZ4kS3T_nUJV4IQ,1183
thinc/backends/cpu_kernels.hh,sha256=HAsiPua-DxcnkfyHv6Hu9w3GW62xFGpoA8Ha2xkzOn8,15083
thinc/backends/cupy_ops.py,sha256=0a6SX-Up1ap3lcEKCITsu3Dn97o4LPOA-0X0urNAWdQ,13316
thinc/backends/linalg.cp311-win_amd64.pyd,sha256=QjgV_BLqEvZvNzaG4MveQhi3WSB0irzfXY3Zr5_BQkw,40960
thinc/backends/linalg.cpp,sha256=KMSCOy6X9-3qMOv1U3_GGeTPq2elL3q_UhdYaRCWtks,362822
thinc/backends/linalg.pxd,sha256=kuPImd1FJK6aPfU9KSzzZDZ7blB8AavQcJt4rO6yu1c,8641
thinc/backends/linalg.pyx,sha256=gwyyrPduXLZYUgyOUR3cNksiC9xSOPHVqArqHnAQtrU,82
thinc/backends/mps_ops.py,sha256=BKGmtdIRGaWv1MeRxNCNhrbR1IHjcLrUBrhn3rZ7pVs,635
thinc/backends/numpy_ops.cp311-win_amd64.pyd,sha256=W-QUaHvrK3stdRGX2hOXJforPPccfaPqpgX0GileI30,604672
thinc/backends/numpy_ops.cpp,sha256=Vg9da7uBnsOKVYLkLdN-XgeNmsf5OWvJOyfpmbT_5jE,3151004
thinc/backends/numpy_ops.pxd,sha256=bgeqd5XI-kKe_0BWDPk1K6gt7WhZlLQkWy1QcoY1Eig,2039
thinc/backends/numpy_ops.pyx,sha256=PwnEzLqPkAkVWUyT8Y1MKGtR4Zh5x4C78I7cH7Gt6s0,41227
thinc/backends/ops.py,sha256=FA5gIEDgJtjvij8qtAfdJr9Ejni3vqTME5v8uiR_oac,60410
thinc/compat.py,sha256=c3EUSp8x2G-L47j5oLAHKdNNlLu2oRZsamZ0xq7O20Q,3160
thinc/config.py,sha256=b4Z0WJQjKFA30VDHkCy8jIPnd7hEJx0DsHjmeC2C9nY,1311
thinc/extra/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/extra/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/extra/__pycache__/__init__.cpython-311.pyc,,
thinc/extra/search.cp311-win_amd64.pyd,sha256=x4Z0UM9nuXHrYq09_8yypgfeYjiU-S4iP341eSg1KbY,122880
thinc/extra/search.cpp,sha256=cPLyz_vx60OfvZFdMJnh3orBDpI7PoCg5cfH-di4iJU,776691
thinc/extra/search.pxd,sha256=fGmd9Zhr3F0m3LcCDwgyIniLJ5Xy7pakHL4P3dMh9ko,2742
thinc/extra/search.pyx,sha256=RxaU8cAVrY4x42laeVHndn-GG5IQSefrcX2UY78k0k0,12533
thinc/extra/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/extra/tests/__pycache__/__init__.cpython-311.pyc,,
thinc/extra/tests/c_test_search.pyx,sha256=gvtewaJ9FOOOV4lGCgT2YI1DCwTxKBtPl6GcBj4RLKM,2490
thinc/initializers.py,sha256=Q0cRFN59B8mrtvmlfeS7FJ8jmRGfmzFy8E482pion_g,3957
thinc/layers/__init__.py,sha256=DMthDdLZu8znrorDPn-5zCi4Dhz8jcKE34cf5vBT3oo,4566
thinc/layers/__pycache__/__init__.cpython-311.pyc,,
thinc/layers/__pycache__/add.cpython-311.pyc,,
thinc/layers/__pycache__/array_getitem.cpython-311.pyc,,
thinc/layers/__pycache__/bidirectional.cpython-311.pyc,,
thinc/layers/__pycache__/cauchysimilarity.cpython-311.pyc,,
thinc/layers/__pycache__/chain.cpython-311.pyc,,
thinc/layers/__pycache__/clipped_linear.cpython-311.pyc,,
thinc/layers/__pycache__/clone.cpython-311.pyc,,
thinc/layers/__pycache__/concatenate.cpython-311.pyc,,
thinc/layers/__pycache__/dish.cpython-311.pyc,,
thinc/layers/__pycache__/dropout.cpython-311.pyc,,
thinc/layers/__pycache__/embed.cpython-311.pyc,,
thinc/layers/__pycache__/expand_window.cpython-311.pyc,,
thinc/layers/__pycache__/gelu.cpython-311.pyc,,
thinc/layers/__pycache__/hard_swish.cpython-311.pyc,,
thinc/layers/__pycache__/hard_swish_mobilenet.cpython-311.pyc,,
thinc/layers/__pycache__/hashembed.cpython-311.pyc,,
thinc/layers/__pycache__/layernorm.cpython-311.pyc,,
thinc/layers/__pycache__/linear.cpython-311.pyc,,
thinc/layers/__pycache__/list2array.cpython-311.pyc,,
thinc/layers/__pycache__/list2padded.cpython-311.pyc,,
thinc/layers/__pycache__/list2ragged.cpython-311.pyc,,
thinc/layers/__pycache__/logistic.cpython-311.pyc,,
thinc/layers/__pycache__/lstm.cpython-311.pyc,,
thinc/layers/__pycache__/map_list.cpython-311.pyc,,
thinc/layers/__pycache__/maxout.cpython-311.pyc,,
thinc/layers/__pycache__/mish.cpython-311.pyc,,
thinc/layers/__pycache__/multisoftmax.cpython-311.pyc,,
thinc/layers/__pycache__/mxnetwrapper.cpython-311.pyc,,
thinc/layers/__pycache__/noop.cpython-311.pyc,,
thinc/layers/__pycache__/padded2list.cpython-311.pyc,,
thinc/layers/__pycache__/parametricattention.cpython-311.pyc,,
thinc/layers/__pycache__/parametricattention_v2.cpython-311.pyc,,
thinc/layers/__pycache__/pytorchwrapper.cpython-311.pyc,,
thinc/layers/__pycache__/ragged2list.cpython-311.pyc,,
thinc/layers/__pycache__/reduce_first.cpython-311.pyc,,
thinc/layers/__pycache__/reduce_last.cpython-311.pyc,,
thinc/layers/__pycache__/reduce_max.cpython-311.pyc,,
thinc/layers/__pycache__/reduce_mean.cpython-311.pyc,,
thinc/layers/__pycache__/reduce_sum.cpython-311.pyc,,
thinc/layers/__pycache__/relu.cpython-311.pyc,,
thinc/layers/__pycache__/remap_ids.cpython-311.pyc,,
thinc/layers/__pycache__/residual.cpython-311.pyc,,
thinc/layers/__pycache__/resizable.cpython-311.pyc,,
thinc/layers/__pycache__/siamese.cpython-311.pyc,,
thinc/layers/__pycache__/sigmoid.cpython-311.pyc,,
thinc/layers/__pycache__/sigmoid_activation.cpython-311.pyc,,
thinc/layers/__pycache__/softmax.cpython-311.pyc,,
thinc/layers/__pycache__/softmax_activation.cpython-311.pyc,,
thinc/layers/__pycache__/strings2arrays.cpython-311.pyc,,
thinc/layers/__pycache__/swish.cpython-311.pyc,,
thinc/layers/__pycache__/tensorflowwrapper.cpython-311.pyc,,
thinc/layers/__pycache__/torchscriptwrapper.cpython-311.pyc,,
thinc/layers/__pycache__/tuplify.cpython-311.pyc,,
thinc/layers/__pycache__/uniqued.cpython-311.pyc,,
thinc/layers/__pycache__/with_array.cpython-311.pyc,,
thinc/layers/__pycache__/with_array2d.cpython-311.pyc,,
thinc/layers/__pycache__/with_cpu.cpython-311.pyc,,
thinc/layers/__pycache__/with_debug.cpython-311.pyc,,
thinc/layers/__pycache__/with_flatten.cpython-311.pyc,,
thinc/layers/__pycache__/with_flatten_v2.cpython-311.pyc,,
thinc/layers/__pycache__/with_getitem.cpython-311.pyc,,
thinc/layers/__pycache__/with_list.cpython-311.pyc,,
thinc/layers/__pycache__/with_nvtx_range.cpython-311.pyc,,
thinc/layers/__pycache__/with_padded.cpython-311.pyc,,
thinc/layers/__pycache__/with_ragged.cpython-311.pyc,,
thinc/layers/__pycache__/with_reshape.cpython-311.pyc,,
thinc/layers/__pycache__/with_signpost_interval.cpython-311.pyc,,
thinc/layers/add.py,sha256=VPdmhElJZm-xT4zfzaReyeOCEzZPvj6pSQiUISk8UC0,2313
thinc/layers/array_getitem.py,sha256=vuhdnHkXyiUKODZVu-ZeGOkhTa36N0IfhaBgDAILRwg,1638
thinc/layers/bidirectional.py,sha256=gJKGVDXw53tn_UeoU4a7hP83whd6ZrzhV8pwA1zddYc,2262
thinc/layers/cauchysimilarity.py,sha256=w9lHjm1mgGQ8NFpv2Z-4HgvLiN4Ekf54TP2I2f00Irg,1804
thinc/layers/chain.py,sha256=5KD68qOnpuA4mVilV2sHOgM-vqgLd4h4pjNP6gmp-lU,3546
thinc/layers/clipped_linear.py,sha256=n0svqEfytzujXFzK1Ghug1ldWtOPU-SHPt_yGTMxHVI,4612
thinc/layers/clone.py,sha256=s9UutT2CeQf1RsK2mJF1Bb6UiEfUuzi8beb58t27DIg,672
thinc/layers/concatenate.py,sha256=8x11-6PBYubMu2sKH5w5zpBZ62OCjvpRjfikYEu5m34,5343
thinc/layers/dish.py,sha256=oDxtFhTBzT2pofh1JoZLUyuXQBW79Vi9hgUzwfPRn14,2183
thinc/layers/dropout.py,sha256=kjhqUAFStGJluxiCK93UhpYj5koWlU8nhRO2k1KswOA,3042
thinc/layers/embed.py,sha256=Bt5pWVaKOlPZt_CvxVx-Mrv98s-y19ZIB2iWwAcAW8g,2841
thinc/layers/expand_window.py,sha256=Dmh_geLvs6hCHll8SAbO-AGQfW77uBen-v1cQUQaLh4,1697
thinc/layers/gelu.py,sha256=M7F-yCEPj5NpEKZqFO7psTrbjPzIKf9RtFWLsIDiEwg,2190
thinc/layers/hard_swish.py,sha256=W_-lHod8Qy_BrLAV0QliQtFzuZ9E4c-BpzhrG-haOrM,2217
thinc/layers/hard_swish_mobilenet.py,sha256=486HLIgWsfIEGZ2SZvxDS8R1bVIkxaP3f_S8sUd2KLI,2264
thinc/layers/hashembed.py,sha256=ABZ3QTt_XlHoxIJPW3COyaYLNlev8AcpnRG2ae9cRsY,3716
thinc/layers/layernorm.py,sha256=IhPHohbrWjtI7NItEZL39OArscRL_UMrtfQN75LsqWA,2621
thinc/layers/linear.py,sha256=ZCFnQfeIO9SfT552szQBke3103U4vxEHBvtMWUA1PaY,1781
thinc/layers/list2array.py,sha256=6jWCDtgJ892BlLz0aSXklki2Nu4kG0tzTAN5SsCAHhw,907
thinc/layers/list2padded.py,sha256=uguepFCFzu7Af1skPEKgdRjtbQU0ZXk8uJjwfkThxOs,645
thinc/layers/list2ragged.py,sha256=2HLo5B5toUA8q1eDRvANdmWWNOD3uPFVEIxWN2NrrKE,888
thinc/layers/logistic.py,sha256=KEFCo2JxfJyUFeG9k-KK80AnVZ7QFyn5GtHMOy1kLHw,634
thinc/layers/lstm.py,sha256=nQtDCu6ML6w0Qp5qwoPHA2N6gAy--h5rDFA2_p9p4RM,6633
thinc/layers/map_list.py,sha256=uO7hkODEg6vpCTQ5rIQBkX6vlt0tGu-I6mFJugNdiRs,1063
thinc/layers/maxout.py,sha256=b1Zn84wVruEwvBIKJDnMt9guqp1A8K96i8KXw4fuXCo,2676
thinc/layers/mish.py,sha256=s4PEICpUfT_sguXt788XRdlvHbiyh25Oxc9MKFbIjaU,2345
thinc/layers/multisoftmax.py,sha256=5WG6sxGSqHRqhrf-0Naq9qddr9UsJLwzNBS4D29n-IA,1824
thinc/layers/mxnetwrapper.py,sha256=YI_HGyaC_wMibgR-stjzKt2XI1_kbGU1fgJI52NFSno,4397
thinc/layers/noop.py,sha256=fpjpH7_v8OOrZ_jOUxp9ssSTT3-hHT1Jd1IAh_kjswA,532
thinc/layers/padded2list.py,sha256=CJt-5lxYcm2a_5c1UQ1cbR7_wEfhWpLF2NjprrhgAPs,729
thinc/layers/parametricattention.py,sha256=8kjtg9VXv2Mkxk6yG29bntXo3WfHcZn6w0F2lGsdKY8,2169
thinc/layers/parametricattention_v2.py,sha256=QWomX-SSlWcFBHlKsXRodFZFQToXc4OY4JNP30mcQDw,3072
thinc/layers/premap_ids.cp311-win_amd64.pyd,sha256=lraAk_9QouxWopwntJUXudelZP7aDa1wYgEnZxop4Xw,165888
thinc/layers/premap_ids.cpp,sha256=p71MHplCvCIUaadFDaNKwHRovNZ_BTrgwJtph2ES-H8,1126461
thinc/layers/premap_ids.pyx,sha256=Rx-aLJ-QViWx1-7B-9C9-hwnxv6b0aHziNmoaZeDCCk,2438
thinc/layers/pytorchwrapper.py,sha256=uHzvu1yuh4idvFs3qw-NvzDPS-aD9cvUgN3XYJW014I,12758
thinc/layers/ragged2list.py,sha256=U5H1pYticD9xd94PSgb1j91FdnrLOZ--eKrfO6RX298,798
thinc/layers/reduce_first.py,sha256=OdgfjUJNlSE-hXSPAZT5UMP3IHbtEH1KMjyoainrEXQ,845
thinc/layers/reduce_last.py,sha256=ur6OxTP-8o6n729XH1FbU36PWtomVKm1MOdZNjSz_KQ,825
thinc/layers/reduce_max.py,sha256=wnIG3VWwJ53wI4qwJSkeq9vuYGWXRkMRRZrSgZQ3Kq4,746
thinc/layers/reduce_mean.py,sha256=4SYxnSjscp0PS1cmAI1YxGhi4yg6Zn351OMnitABcms,739
thinc/layers/reduce_sum.py,sha256=n_wvvUMpECNs4lXyF8a8a9tZvyvPpEsGFnrrA3Nf23M,732
thinc/layers/relu.py,sha256=pn6oI6jXJWtKsCtCWmLZvKbTnU0KGv1BgRS42x7slEo,2128
thinc/layers/remap_ids.py,sha256=4bq4WTfw3M2j622frKOY67vtQO38uRQLDmrxXLRAEX4,3169
thinc/layers/residual.py,sha256=GNUCx84fTNbDx0dqUanRv1rvXhlO6PFpZgmY1bfA7PI,2156
thinc/layers/resizable.py,sha256=OHZDc1cC17Me5QC096MezyGboZXg1jde1jrBCAGtrrk,2848
thinc/layers/siamese.py,sha256=3bYwY8PBQ_ZR0MzuL2Ix45tdvFYcKqA-zaup5FzFi5k,1667
thinc/layers/sigmoid.py,sha256=mnldqthR7XOjEiSbSbfzzza0iCIL5xk-6Pe7N2E0hzY,2010
thinc/layers/sigmoid_activation.py,sha256=TXIzLlsgVMK2ykv0dB4Y-tx1f10jQ6iVv6ZzsCzy2OA,676
thinc/layers/softmax.py,sha256=2jB8FjH1sl-jP6hpF6CtLDHHWIjpIuk9YsPt43N9Dqw,3352
thinc/layers/softmax_activation.py,sha256=e2cae-pjYRj19GTE__2TYlx0wWW7TpzoAOKeX30ADBc,564
thinc/layers/sparselinear.cp311-win_amd64.pyd,sha256=oucd8LfXUhcqdor4xdGwcvKUdHhMdfYJdwfnVXD_SLE,195584
thinc/layers/sparselinear.cpp,sha256=nAfPEghQryMXgLwlh_B5pdIRh_zI5i56pPa-dVWtqVY,1417826
thinc/layers/sparselinear.pyx,sha256=u6afY3tXNC_HgwGnRPbNL5zubkf05tEQtWQFNCwBwM0,8391
thinc/layers/strings2arrays.py,sha256=a-PKPmmqJpYANtDllVCz0gWQItZhmVseshfkolvlZVU,983
thinc/layers/swish.py,sha256=FsfiwUBwh_DFeCJZXbcv2pxPTc8CzfIywYjWXDDSjkY,2198
thinc/layers/tensorflowwrapper.py,sha256=cg2h8OywsXeI5khltbaTmEiH_CrAajsg9Wn1GKF9VCo,6732
thinc/layers/torchscriptwrapper.py,sha256=0SWmVTkkmeGuxJON4-mNEHImB-8ikT9inYzcX8KX-ak,3369
thinc/layers/tuplify.py,sha256=2T6OT23Kes8N3Lzls19m7P5Zaw_OsuCb7ThdwLOJwt8,2088
thinc/layers/uniqued.py,sha256=nhOCSQ0hI0M6A0eyqOEVnRuHwhX24UZxCkl5Qp1Sz70,2126
thinc/layers/with_array.py,sha256=wR594GVwxLRXN25nJlsOGHFywBmS_ZmbqPgCmULmke4,3688
thinc/layers/with_array2d.py,sha256=1EDVP1H0MzwzW5CtDkI-GQgTfSGx7PbT7wmSWWYsj1M,4261
thinc/layers/with_cpu.py,sha256=eLhpHSAEKpZ8ut_V66h_djfXKyBtgmiIJNkfKpxlqmg,1485
thinc/layers/with_debug.py,sha256=np_LDGA2bc2COg5WrTnESw0O2K8TNCW36czm0O4lCAU,1377
thinc/layers/with_flatten.py,sha256=ZKxaMHMqE4uXKSRuHuzJ_SC5qw9tHrQodTNOmAF-AQU,1727
thinc/layers/with_flatten_v2.py,sha256=tONPGDg2BJUf8rXksu3brpZZWa5dXeI-u7riOk7gVNY,2019
thinc/layers/with_getitem.py,sha256=RX6CF0ez6KwnIAeKF4UEo9zwYVAH9QlOlR-hppS7MhM,1210
thinc/layers/with_list.py,sha256=-porFhjHgt6PxJJitS9moJ6mmFb8ppnjecMcM8dHJNg,2980
thinc/layers/with_nvtx_range.py,sha256=Y-CJmudzf-jyYQh36W6K7AIdLppUdrY7yV7mQET6hkA,1337
thinc/layers/with_padded.py,sha256=IoSglfea7ck0DfutiohRq2mP3yS9Nr-cvPGJJ4eW82g,4999
thinc/layers/with_ragged.py,sha256=-jfA0ZV3KJOM3eOz1dK7YOHr9e9EjePiXfLCiw2a_Zg,4574
thinc/layers/with_reshape.py,sha256=xRc8OJ845YwlttV2AAsZA7rjGK9wQAkjDdUj94zoHVU,1847
thinc/layers/with_signpost_interval.py,sha256=wyuBAUoZDeZCwDareW7BZNEqTHSpmPubb5VAQ68tX0o,1560
thinc/loss.py,sha256=V9c3gbZOA378JFPRb-uFKkjjsQa8cYT7-XtgZWxhmr4,16747
thinc/model.py,sha256=hxq-TTOf7tk2nB2ZTjQytm728bNTH84QXcDY5T_3aGs,35814
thinc/mypy.py,sha256=QJSAm_YRfSE-PvZGWND_ISvJpG3nezKwwNRKuM1aqAA,11733
thinc/optimizers.py,sha256=RaOcE2_6ibpw1MZI7-ptx2KFgqSLUZlGGCcZjPoQMDY,12234
thinc/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/schedules.py,sha256=KUPmQHpzs9E_ybY4IMqcd56AOQ5h7TvOhr1FhTiQs0o,3848
thinc/shims/__init__.py,sha256=29fKxSl5nhBK_oODrLqIeADSmAqZnM340jT-5Z8Jewc,481
thinc/shims/__pycache__/__init__.cpython-311.pyc,,
thinc/shims/__pycache__/mxnet.cpython-311.pyc,,
thinc/shims/__pycache__/pytorch.cpython-311.pyc,,
thinc/shims/__pycache__/pytorch_grad_scaler.cpython-311.pyc,,
thinc/shims/__pycache__/shim.cpython-311.pyc,,
thinc/shims/__pycache__/tensorflow.cpython-311.pyc,,
thinc/shims/__pycache__/torchscript.cpython-311.pyc,,
thinc/shims/mxnet.py,sha256=U7R_FOsvdmIk7EQaxycqfTLHmynqhmkQqWFp-6Qxs6M,4438
thinc/shims/pytorch.py,sha256=KYvv3ib8_h0t2IjDZx2S6_Y0EyGEyUhTTqI-b_vgLEE,10239
thinc/shims/pytorch_grad_scaler.py,sha256=pHDkOCcxTxn23H-BnAJ78DZrthx_dTtrzufgxxJdXpg,6241
thinc/shims/shim.py,sha256=vEaL773EFEeH8llhlJjU_xN45v1WFhgekIJGiBiC3Hc,2556
thinc/shims/tensorflow.py,sha256=XxpehecJDeWJ4qb3qk1s65RkvNotKq1NXu8WisSR6rs,10881
thinc/shims/torchscript.py,sha256=qh9aP-XPqMl7wWU4Z9m_-F1H_VZCnH5MkzJeSSyO0rQ,2528
thinc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/__pycache__/conftest.cpython-311.pyc,,
thinc/tests/__pycache__/enable_mxnet.cpython-311.pyc,,
thinc/tests/__pycache__/enable_tensorflow.cpython-311.pyc,,
thinc/tests/__pycache__/strategies.cpython-311.pyc,,
thinc/tests/__pycache__/test_config.cpython-311.pyc,,
thinc/tests/__pycache__/test_examples.cpython-311.pyc,,
thinc/tests/__pycache__/test_import__all__.cpython-311.pyc,,
thinc/tests/__pycache__/test_indexing.cpython-311.pyc,,
thinc/tests/__pycache__/test_initializers.cpython-311.pyc,,
thinc/tests/__pycache__/test_loss.cpython-311.pyc,,
thinc/tests/__pycache__/test_optimizers.cpython-311.pyc,,
thinc/tests/__pycache__/test_schedules.cpython-311.pyc,,
thinc/tests/__pycache__/test_serialize.cpython-311.pyc,,
thinc/tests/__pycache__/test_types.cpython-311.pyc,,
thinc/tests/__pycache__/test_util.cpython-311.pyc,,
thinc/tests/__pycache__/util.cpython-311.pyc,,
thinc/tests/backends/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/backends/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/backends/__pycache__/test_mem.cpython-311.pyc,,
thinc/tests/backends/__pycache__/test_ops.cpython-311.pyc,,
thinc/tests/backends/test_mem.py,sha256=gD6S4ohD0qbE0mxs3Kgf58GWZtI2LYe6ek0n2M7ZMuU,371
thinc/tests/backends/test_ops.py,sha256=8mvuhtlJ29owk801q7KMJDFbFfN8VTJwHuPfFeNQvRg,54460
thinc/tests/conftest.py,sha256=pLOWV0lSH4XMlPoXHujhr_oLfwF3DuxA3piGnrmotfU,2277
thinc/tests/enable_mxnet.py,sha256=BBLTOuSm2LmRLVj1mGZh1B_w_LOe-cq0NgKjlWCWckk,98
thinc/tests/enable_tensorflow.py,sha256=6pKd-k0vr1PF_JGRpMRM__qMVFdl_pLkxsEBU_gMq3Y,108
thinc/tests/extra/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/extra/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/extra/__pycache__/test_beam_search.cpython-311.pyc,,
thinc/tests/extra/test_beam_search.py,sha256=5m_rwsKvdzm_6ZtFsgh3kddxoCj6nskCicHwZymParc,93
thinc/tests/layers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/layers/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_basic_tagger.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_combinators.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_feed_forward.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_hash_embed.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_layers_api.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_linear.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_lstm.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_mappers.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_mnist.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_mxnet_wrapper.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_parametric_attention_v2.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_pytorch_wrapper.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_reduce.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_resizable.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_shim.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_softmax.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_sparse_linear.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_tensorflow_wrapper.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_torchscriptwrapper.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_transforms.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_uniqued.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_with_debug.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_with_flatten.cpython-311.pyc,,
thinc/tests/layers/__pycache__/test_with_transforms.cpython-311.pyc,,
thinc/tests/layers/test_basic_tagger.py,sha256=mqO1tt40NWM9Pf4mseeJ4AQckPHMn6ia9vBl_TmUG4g,2515
thinc/tests/layers/test_combinators.py,sha256=_XZHCh_7WBTdMhUiiGGrwWwsmGS5yIygLYQCEWUfgTU,8126
thinc/tests/layers/test_feed_forward.py,sha256=9qDyMDKJUM2ccntrrNBkheWly50NsUk4a4b38B-M70c,5637
thinc/tests/layers/test_hash_embed.py,sha256=lHVCsCA9EfELV0c7biTm-3P1V7SA8FWlUnKVKOMYLTg,552
thinc/tests/layers/test_layers_api.py,sha256=iBnySO4xyt_tPkOLKnPASrLmhTKJ7C3kJ-wY9xV23lw,10210
thinc/tests/layers/test_linear.py,sha256=v_RKUm0y5jFPI5rl3OoJ0EaDgByZ-w5XO6XMzsK-GNI,7899
thinc/tests/layers/test_lstm.py,sha256=uU7a8m4xRbODptxNtH5GK5EBIXbg9_zux1b0B4NTLLM,5828
thinc/tests/layers/test_mappers.py,sha256=8DnMDtkxVL9teUjjn7bN4GgauwlQ4S6N2R1QFdj5RaI,1706
thinc/tests/layers/test_mnist.py,sha256=zx1OTVe4Y8c3DIObqMgfyFdzjN28EqMm9sAruBdc_iQ,3785
thinc/tests/layers/test_mxnet_wrapper.py,sha256=CpoKmPjM32NFkLBt0pAhEjMebplvvTHdtgI_C6lPB5E,6508
thinc/tests/layers/test_parametric_attention_v2.py,sha256=iXLeIWfu1nGjOwAXZaL2U59z-Jwp-1MvrYkr0qeLL90,295
thinc/tests/layers/test_pytorch_wrapper.py,sha256=QPFBJrG_Rs8Ifv9SVFsUqaflXNbbwz3CGhSHza9J9gU,7166
thinc/tests/layers/test_reduce.py,sha256=7C0wUzbHoII8QdKcEoERp9v0RwseC-EO9EVFORI-5ig,3817
thinc/tests/layers/test_resizable.py,sha256=c_K3qF_VpS14YagzOZpWITRlk_i8jm06m-13mf5w6nA,908
thinc/tests/layers/test_shim.py,sha256=RfEbxemHzxWxB2sPH4oROXFYIWjSEMYU2NlZa2D75iQ,980
thinc/tests/layers/test_softmax.py,sha256=ECmaj8g-kPzFlnZKwJSEEDn1kXZ8jtt95U1BUP8vDeA,2961
thinc/tests/layers/test_sparse_linear.py,sha256=UEIAj9EtVYCVjMRiu-zsd2ifMd8_1SQvaOOeTqMPv04,2370
thinc/tests/layers/test_tensorflow_wrapper.py,sha256=L8nsG20rf88f1m4pLg435qoJnB63RDVnDihSKRvDjLY,13525
thinc/tests/layers/test_torchscriptwrapper.py,sha256=1P1CgEDS5czFbZZ7hzdTGrcA972gI5LQ40qUnOYiJ9E,834
thinc/tests/layers/test_transforms.py,sha256=4gdkanhD2cnvB6rXldoZ4ZK6Y56M4GdURMdieqAmLgY,2055
thinc/tests/layers/test_uniqued.py,sha256=OWoof_geI9abeVTMnuT0SB162Ai4KTC15fBVoLs6BVQ,2318
thinc/tests/layers/test_with_debug.py,sha256=1FzW2li2orBOmAu7O0pW8w-WVPfAFQpC0LNVrw1m14c,828
thinc/tests/layers/test_with_flatten.py,sha256=byvCiVvxHVyVix1YpEdJKT6Cv_TaBOtC9gNIR7X8iVU,902
thinc/tests/layers/test_with_transforms.py,sha256=Cazbpge4wNbTIK3YWYtJiM00rY0m9PXGSrMV9-ERC7g,10468
thinc/tests/model/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/model/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/model/__pycache__/test_model.cpython-311.pyc,,
thinc/tests/model/__pycache__/test_validation.cpython-311.pyc,,
thinc/tests/model/test_model.py,sha256=25nhoI0NR7wHjxb0EApS2MjF4ZFOuijGHtiGIsngDmE,20587
thinc/tests/model/test_validation.py,sha256=7YQOsp1b6BvHWeQ-RY7YOuNu75bLEFtGADc3z5uKIo0,1598
thinc/tests/mypy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/mypy/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/mypy/__pycache__/test_mypy.cpython-311.pyc,,
thinc/tests/mypy/configs/mypy-default.ini,sha256=PSZ1M9DCNyH2FnzWHd16kfiNGVVs0m4HZMzzm3MaRB4,203
thinc/tests/mypy/configs/mypy-plugin.ini,sha256=qFlHNcPacAgVtX5Pnwnhe1TvVhlrnnF5IL3L7-u6sGc,227
thinc/tests/mypy/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/mypy/modules/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/mypy/modules/__pycache__/fail_no_plugin.cpython-311.pyc,,
thinc/tests/mypy/modules/__pycache__/fail_plugin.cpython-311.pyc,,
thinc/tests/mypy/modules/__pycache__/success_no_plugin.cpython-311.pyc,,
thinc/tests/mypy/modules/__pycache__/success_plugin.cpython-311.pyc,,
thinc/tests/mypy/modules/fail_no_plugin.py,sha256=iX24AqhUjkZYYPaFehm8ukaBCf5xcugjrXwZy451rUU,172
thinc/tests/mypy/modules/fail_plugin.py,sha256=E0GjqcAbfCaT9PB5-OzAYXOIQjVJ6joreT0T4kY3IiY,585
thinc/tests/mypy/modules/success_no_plugin.py,sha256=J06z-fCZzo9cMDw1GBX902wDJaIh1AfSPOLXEAbZIg8,441
thinc/tests/mypy/modules/success_plugin.py,sha256=FDG20ngMBWv6l_6ipm8eNCj9comSxXcIPsKAf-FV7kU,865
thinc/tests/mypy/outputs/fail-no-plugin.txt,sha256=ijNJi9n06boRz1JXWQbB6tMrWBMJCM-w0dDun0HFY-A,114
thinc/tests/mypy/outputs/fail-plugin.txt,sha256=-Z5qEnQ3RHgz9-n1CRqlTsI_pwFchFtFNUUhThtUA3g,2436
thinc/tests/mypy/outputs/success-no-plugin.txt,sha256=DOtWznityx8YUd-Vo2Y-24oSRZH9FWa7Uc2dNCCcxmc,583
thinc/tests/mypy/outputs/success-plugin.txt,sha256=MR_6qVKJgKaKSFIqo6b8o502kQTkjqtLEzmrBop-AOk,499
thinc/tests/mypy/test_mypy.py,sha256=hHPBuPKBYZ-aW_pLGwpZqHO769lW8NGZ66imv3w1wyU,3163
thinc/tests/regression/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/regression/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/regression/__pycache__/test_issue208.cpython-311.pyc,,
thinc/tests/regression/__pycache__/test_issue564.cpython-311.pyc,,
thinc/tests/regression/issue519/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/regression/issue519/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/regression/issue519/__pycache__/program.cpython-311.pyc,,
thinc/tests/regression/issue519/__pycache__/test_issue519.cpython-311.pyc,,
thinc/tests/regression/issue519/program.py,sha256=b51mTjBOIIcMC6k3cHtwpAcyQawhDvKF30YF5YJzhH8,496
thinc/tests/regression/issue519/test_issue519.py,sha256=yUIO_cJjj7xTGyUWqW9LEYRmmIIL_RR9m00Sot50gco,846
thinc/tests/regression/test_issue208.py,sha256=xaxUToQQq66JzMT4JflwJruAE9zRDG-Lln91oZ2xFwM,338
thinc/tests/regression/test_issue564.py,sha256=0sPdlk8WDJxRDNUMbUz7IlJDpgKUHzNnOmHR-s2Q-nk,541
thinc/tests/shims/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
thinc/tests/shims/__pycache__/__init__.cpython-311.pyc,,
thinc/tests/shims/__pycache__/test_pytorch_grad_scaler.cpython-311.pyc,,
thinc/tests/shims/test_pytorch_grad_scaler.py,sha256=aNmWAF1bD4PiO-WjrxtrQYsxSxzCvAv8IrOsOu3jn34,3458
thinc/tests/strategies.py,sha256=RNLp8PRzIGUoUb3IWFumn_HHPwfn18oNsxj_SfqM7lI,3712
thinc/tests/test_config.py,sha256=WXleppolJA8w_zuEr_y3iiNVa6iyfuTp49zfJ_xukoo,6039
thinc/tests/test_examples.py,sha256=K6R6aUUATGJSVy4IEuc4jye2pJuMqnCrsj891Lqzwj0,1864
thinc/tests/test_import__all__.py,sha256=SVybWbYk7LusfRQnfYVgCdu5wB4JeawXM5XvYoytHWg,1853
thinc/tests/test_indexing.py,sha256=xjtc1amcbr5sJU9ABcUpw72PRELk7efMP2oHPj5EJp0,1962
thinc/tests/test_initializers.py,sha256=5zpaIU47iNjcQ_cKT8TynYEsQrNWOLUAipQViUmQ29s,1026
thinc/tests/test_loss.py,sha256=GXHNfPltcot7OsUUEvJUwj8ueDabWrTEKC2Hh9nG-iI,12415
thinc/tests/test_optimizers.py,sha256=mEOvG7NBSoe8TX9F7bs0q-FlA48_DgZWl6i3VHqPPvM,3148
thinc/tests/test_schedules.py,sha256=Zkx4WHXp4Rzdxv_pRrj3KQTxWM9lsa4ascHelmXlTyQ,1773
thinc/tests/test_serialize.py,sha256=-VGnpB6YPBFR0YcoDIxUi83VOGNb7hZ7Ss5Z3Gjgkrk,6997
thinc/tests/test_types.py,sha256=W3FpWYbLbm435c5Ogpf3cLX0IHaU_b540CZOQAI-6SA,1597
thinc/tests/test_util.py,sha256=QoSWWTmbjuYTBgiDk2L1gWY9ec4U8BAYUI_JT5jL_gs,5812
thinc/tests/util.py,sha256=YEfSIneLivmxDFT4zAJ--P8Oxh9eQNML_DKVZA8DZSE,3721
thinc/types.py,sha256=sFPpyfNVvphFNY9GVtSKY2Z6VWIzccXBQr4OWG4Up6c,49961
thinc/util.py,sha256=Rpgzr8P4parsOpaDDXGzqgom9UiIpqH9Qh3P-upKWa8,22078
