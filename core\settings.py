
import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    PROJECT_NAME: str = "Binance Trading Bot"
    PROJECT_VERSION: str = "0.1.0"

    # API settings
    BINANCE_API_KEY: str = os.getenv("BINANCE_API_KEY", "")
    BINANCE_SECRET_KEY: str = os.getenv("BINANCE_SECRET_KEY", "")

    # Database settings
    MONGO_URI: str = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
    MONGO_DB_NAME: str = os.getenv("MONGO_DB_NAME", "binance_trading_bot")

    # Trading parameters (examples, can be moved to strategy config)
    DEFAULT_SYMBOL: str = "BTCUSDT"
    DEFAULT_INTERVAL: str = "1m"
    DEFAULT_LEVERAGE: int = 10

    # Ensure API keys are loaded
    if not BINANCE_API_KEY or not BINANCE_SECRET_KEY:
        print("Warning: BINANCE_API_KEY or BINANCE_SECRET_KEY not set in .env file.")

settings = Settings()


