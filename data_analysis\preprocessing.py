
import pandas as pd
from sklearn.preprocessing import MinMaxScaler

class DataPreprocessor:
    def __init__(self):
        self.scaler = MinMaxScaler()

    def preprocess_klines(self, df: pd.DataFrame):
        """
        Preprocesses kline data for ML models.
        Assumes df has columns like ["open", "high", "low", "close", "volume"].
        """
        if df.empty:
            return pd.DataFrame()

        # Convert relevant columns to numeric types if they are not already
        numeric_cols = ["open", "high", "low", "close", "volume"]
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Drop any rows with NaN values that resulted from coercion
        df.dropna(subset=numeric_cols, inplace=True)

        if df.empty:
            return pd.DataFrame()

        # Scale numerical features
        df_scaled = pd.DataFrame(self.scaler.fit_transform(df[numeric_cols]), columns=numeric_cols, index=df.index)

        # You might want to add more feature engineering here, e.g., technical indicators
        # For now, just scaling basic price and volume data

        return df_scaled

    def inverse_transform_price(self, scaled_price):
        """
        Inverse transforms a scaled price back to its original scale.
        This assumes the scaler was fitted on a DataFrame containing a 'close' column.
        """
        # Create a dummy array with the same number of features as the original data
        # and place the scaled_price in the 'close' column position.
        # This is a bit hacky but necessary for MinMaxScaler if it was fitted on multiple features.
        dummy_row = [0.0] * len(self.scaler.feature_names_in_)
        try:
            close_idx = list(self.scaler.feature_names_in_).index("close")
            dummy_row[close_idx] = scaled_price
        except ValueError:
            print("Warning: 'close' column not found in scaler's fitted features. Cannot inverse transform.")
            return scaled_price # Return as is if 'close' column was not used for fitting

        inverted_data = self.scaler.inverse_transform([dummy_row])
        return inverted_data[0][close_idx]

# Example Usage
if __name__ == '__main__':
    # Create some dummy kline data
    data = {
        'open': [100, 102, 105, 103, 106],
        'high': [103, 106, 107, 105, 108],
        'low': [99, 101, 104, 102, 105],
        'close': [102, 105, 103, 106, 107],
        'volume': [1000, 1200, 1100, 1300, 1050]
    }
    df = pd.DataFrame(data)

    preprocessor = DataPreprocessor()
    df_scaled = preprocessor.preprocess_klines(df.copy())

    print("Original DataFrame:")
    print(df)
    print("\nScaled DataFrame:")
    print(df_scaled)

    # Example of inverse transform (assuming 'close' was the 3rd column in numeric_cols)
    # This is a simplified example, in a real scenario, you'd need to ensure the scaler
    # is correctly fitted and the index for inverse transform matches.
    if not df_scaled.empty:
        original_close = df['close'].iloc[0]
        scaled_close = df_scaled['close'].iloc[0]
        inverted_close = preprocessor.inverse_transform_price(scaled_close)
        print(f"\nOriginal Close: {original_close}, Scaled Close: {scaled_close}, Inverted Close: {inverted_close}")
