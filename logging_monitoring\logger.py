
import logging
import os
from logging.handlers import RotatingFileHandler

def setup_logging(log_file=\'bot_activity.log\', max_bytes=5*1024*1024, backup_count=5):
    """
    Sets up logging for the application.
    Logs will be written to a file and also to the console.
    """
    # Create logs directory if it doesn\'t exist
    log_dir = \'logs\'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_path = os.path.join(log_dir, log_file)

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO) # Set default logging level

    # Clear existing handlers to prevent duplicate logs
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # Console Handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(\'%(asctime)s - %(levelname)s - %(message)s\')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # File Handler (Rotating)
    file_handler = RotatingFileHandler(
        log_path, maxBytes=max_bytes, backupCount=backup_count
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(\'%(asctime)s - %(name)s - %(levelname)s - %(message)s\')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Example of how to use it in other modules:
    # from logging_monitoring.logger import setup_logging
    # setup_logging()
    # logger = logging.getLogger(__name__)
    # logger.info("This is an info message")
    # logger.error("This is an error message")

    return logger

# Initialize logging when this module is imported
logger = setup_logging()

# Example usage within this file
if __name__ == \'__main__\':
    logger.info("Logging setup complete.")
    logger.debug("This is a debug message (won\'t be shown by default INFO level).")
    logger.warning("This is a warning message.")
    logger.error("This is an error message.")
    logger.critical("This is a critical message.")

    # Test multiple log entries
    for i in range(10):
        logger.info(f"Test log entry {i+1}")

    # Simulate an error
    try:
        1 / 0
    except ZeroDivisionError:
        logger.exception("An error occurred during division.")


