
import os
from dotenv import load_dotenv

load_dotenv()

# Core Configuration for API Keys and Database

# Binance API Credentials
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

# MongoDB Connection
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
MONGO_DB_NAME = os.getenv("MONGO_DB_NAME", "binance_trading_bot")

# Ensure API keys are set
if not BINANCE_API_KEY or not BINANCE_SECRET_KEY:
    raise ValueError("BINANCE_API_KEY and BINANCE_SECRET_KEY must be set in the .env file")

# Other core settings can be added here
# For example, default trading pair, default interval, etc.
DEFAULT_TRADING_PAIR = "BTCUSDT"
DEFAULT_CANDLE_INTERVAL = "1m"


