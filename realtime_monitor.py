#!/usr/bin/env python3
"""
Real-time Monitor - Dashboard đơn giản để monitor bot
"""

import requests
import time
import os
from datetime import datetime

BASE_URL = "http://localhost:8000"

def clear_screen():
    """Clear terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_price_data():
    """Lấy dữ liệu giá real-time"""
    try:
        response = requests.get(f"{BASE_URL}/prices", params={
            "symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT"]
        })
        if response.status_code == 200:
            return response.json().get('data', [])
    except:
        pass
    return []

def get_indicators(symbol):
    """Lấy chỉ báo kỹ thuật"""
    try:
        response = requests.get(f"{BASE_URL}/indicator", params={
            "symbol": symbol,
            "interval": "5m",
            "limit": 50
        })
        if response.status_code == 200:
            data = response.json().get('data', [])
            if data:
                return data[-1]  # Latest data
    except:
        pass
    return {}

def get_trade_signals():
    """Lấy tín hiệu giao dịch gần nhất"""
    try:
        response = requests.get(f"{BASE_URL}/trade-signals", params={"limit": 5})
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return []

def format_price(price):
    """Format giá tiền"""
    if price >= 1000:
        return f"${price:,.2f}"
    else:
        return f"${price:.4f}"

def format_percentage(value, reference):
    """Format phần trăm thay đổi"""
    if reference and reference != 0:
        change = ((value - reference) / reference) * 100
        color = "🟢" if change >= 0 else "🔴"
        return f"{color} {change:+.2f}%"
    return "⚪ N/A"

def get_signal_emoji(rsi):
    """Lấy emoji tín hiệu dựa trên RSI"""
    if rsi is None:
        return "⚪"
    elif rsi > 70:
        return "🔴"  # Overbought
    elif rsi < 30:
        return "🟢"  # Oversold
    else:
        return "🟡"  # Neutral

def display_dashboard():
    """Hiển thị dashboard"""
    clear_screen()
    
    print("=" * 80)
    print("🤖 BINANCE TRADING BOT - REAL-TIME MONITOR")
    print("=" * 80)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 🔒 TEST MODE - NO REAL TRADES")
    print()
    
    # Lấy dữ liệu giá
    price_data = get_price_data()
    
    if price_data:
        print("📊 MARKET PRICES & INDICATORS")
        print("-" * 80)
        print(f"{'Symbol':<10} {'Price':<12} {'RSI':<6} {'Signal':<8} {'MA20':<12} {'MACD':<10}")
        print("-" * 80)
        
        for item in price_data:
            if item.get('success'):
                symbol = item['symbol']
                price = item['price']
                
                # Lấy chỉ báo
                indicators = get_indicators(symbol)
                rsi = indicators.get('rsi')
                ma = indicators.get('ma')
                macd = indicators.get('macd')
                
                # Format dữ liệu
                rsi_str = f"{rsi:.1f}" if rsi else "N/A"
                signal = get_signal_emoji(rsi)
                ma_str = format_price(ma) if ma else "N/A"
                macd_str = f"{macd:.4f}" if macd else "N/A"
                
                print(f"{symbol:<10} {format_price(price):<12} {rsi_str:<6} {signal:<8} {ma_str:<12} {macd_str:<10}")
    
    print()
    
    # Lấy tín hiệu giao dịch
    signals = get_trade_signals()
    
    if signals:
        print("🎯 RECENT TRADE SIGNALS")
        print("-" * 80)
        print(f"{'Time':<20} {'Symbol':<10} {'Signal':<6} {'Price':<12} {'SL':<12} {'TP':<12}")
        print("-" * 80)
        
        for signal in signals[:5]:  # Show last 5 signals
            timestamp = signal.get('timestamp', '')
            if timestamp:
                try:
                    # Parse timestamp
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S %d/%m')
                except:
                    time_str = timestamp[:19] if len(timestamp) > 19 else timestamp
            else:
                time_str = "N/A"
            
            symbol = signal.get('symbol', 'N/A')
            signal_type = signal.get('signal', 'N/A')
            price = signal.get('last_close', 0)
            sl = signal.get('stop_loss', 0)
            tp = signal.get('take_profit', 0)
            
            # Color coding for signals
            if signal_type == "BUY":
                signal_display = "🟢 BUY"
            elif signal_type == "SELL":
                signal_display = "🔴 SELL"
            else:
                signal_display = f"⚪ {signal_type}"
            
            print(f"{time_str:<20} {symbol:<10} {signal_display:<6} {format_price(price):<12} {format_price(sl):<12} {format_price(tp):<12}")
    
    print()
    print("📈 LEGEND:")
    print("🟢 Oversold/Buy Signal | 🔴 Overbought/Sell Signal | 🟡 Neutral | ⚪ No Data")
    print()
    print("⌨️  Press Ctrl+C to stop monitoring")
    print("=" * 80)

def main():
    """Main monitoring loop"""
    print("🚀 Starting Real-time Monitor...")
    print("📡 Connecting to trading bot API...")
    
    # Test connection
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("❌ Cannot connect to trading bot API")
            print(f"   Make sure the bot is running at {BASE_URL}")
            return
    except:
        print("❌ Cannot connect to trading bot API")
        print(f"   Make sure the bot is running at {BASE_URL}")
        return
    
    print("✅ Connected successfully!")
    time.sleep(2)
    
    try:
        while True:
            display_dashboard()
            time.sleep(30)  # Update every 30 seconds
            
    except KeyboardInterrupt:
        clear_screen()
        print("\n🛑 Real-time monitor stopped")
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
