#!/usr/bin/env python3
"""
Demo Summary - Tóm tắt tất cả tính năng test mode
"""

import requests
from datetime import datetime

def check_server():
    """Kiểm tra server"""
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_sample_data():
    """Lấy dữ liệu mẫu"""
    try:
        # Lấy giá hiện tại
        response = requests.get("http://localhost:8000/prices", 
                              params={"symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"]})
        if response.status_code == 200:
            return response.json().get('data', [])
    except:
        pass
    return []

def get_trade_signals():
    """Lấy tín hiệu giao dịch"""
    try:
        response = requests.get("http://localhost:8000/trade-signals", params={"limit": 3})
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return []

def main():
    print("=" * 70)
    print("BINANCE TRADING BOT - DEMO SUMMARY")
    print("=" * 70)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Mode: TEST MODE - No real trades executed")
    print()
    
    # Kiểm tra server
    print("[1] SERVER STATUS")
    print("-" * 30)
    if check_server():
        print("Status: RUNNING")
        print("URL: http://localhost:8000")
        print("Docs: http://localhost:8000/docs")
    else:
        print("Status: NOT RUNNING")
        print("Please start: uvicorn app.main:app --reload")
        return
    print()
    
    # Hiển thị giá hiện tại
    print("[2] CURRENT MARKET PRICES")
    print("-" * 30)
    prices = get_sample_data()
    if prices:
        for item in prices:
            if item.get('success'):
                symbol = item['symbol']
                price = item['price']
                print(f"{symbol}: ${price:,.4f}")
    else:
        print("No price data available")
    print()
    
    # Hiển thị tín hiệu gần nhất
    print("[3] RECENT TRADE SIGNALS")
    print("-" * 30)
    signals = get_trade_signals()
    if signals:
        for signal in signals[:3]:
            symbol = signal.get('symbol', 'N/A')
            signal_type = signal.get('signal', 'N/A')
            price = signal.get('last_close', 0)
            print(f"{symbol}: {signal_type} at ${price:,.4f}")
    else:
        print("No trade signals found")
    print()
    
    # Tính năng có sẵn
    print("[4] AVAILABLE FEATURES")
    print("-" * 30)
    features = [
        "Real-time price monitoring",
        "Technical indicators (RSI, MA, MACD)",
        "Trade signal generation", 
        "Risk management calculations",
        "MongoDB data storage",
        "RESTful API endpoints",
        "Demo trading simulation"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i}. {feature}")
    print()
    
    # Scripts có sẵn
    print("[5] AVAILABLE TEST SCRIPTS")
    print("-" * 30)
    scripts = [
        ("simple_test.py", "Quick test with current prices and indicators"),
        ("test_api_endpoints.py", "Test all API endpoints (fix emoji issue first)"),
        ("realtime_monitor.py", "Live dashboard with 30s updates"),
        ("test_realtime_demo.py", "Continuous bot simulation"),
        ("quick_test.py", "Fast analysis of top 3 symbols")
    ]
    
    for script, description in scripts:
        print(f"python {script}")
        print(f"   -> {description}")
        print()
    
    # Cấu hình an toàn
    print("[6] SAFETY CONFIGURATION")
    print("-" * 30)
    print("TEST_MODE=1 in .env file")
    print("-> Prevents real trading")
    print("-> Uses demo balance ($10,000)")
    print("-> Only logs signals to database")
    print("-> Safe for testing and learning")
    print()
    
    # Hướng dẫn tiếp theo
    print("[7] NEXT STEPS")
    print("-" * 30)
    print("1. Run: python simple_test.py")
    print("   -> Quick verification test")
    print()
    print("2. Run: python realtime_monitor.py")
    print("   -> Live monitoring dashboard")
    print()
    print("3. Configure real API keys when ready:")
    print("   -> Update BINANCE_API_KEY in .env")
    print("   -> Update BINANCE_API_SECRET in .env")
    print("   -> Change TEST_MODE=0 for live trading")
    print()
    print("4. Start with small amounts:")
    print("   -> Test with minimal position sizes")
    print("   -> Monitor performance carefully")
    print()
    
    print("=" * 70)
    print("DEMO SETUP COMPLETE!")
    print("Your trading bot is ready for safe testing.")
    print("=" * 70)

if __name__ == "__main__":
    main()
