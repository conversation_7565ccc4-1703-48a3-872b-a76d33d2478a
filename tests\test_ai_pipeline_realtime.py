# test/test_ai_pipeline_realtime.py (<PERSON><PERSON><PERSON> bộ đặt lệnh thực chiến Binance Futures với pipeline AI realtime)
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import requests
import threading
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from data_analysis.backtest import backtest_signals
from db.session import get_database
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
from trade_execution.order_manager import open_position, close_position
import pandas as pd
import numpy as np

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
import tensorflow as tf
warnings_filter = threading.Thread(target=lambda: __import__('warnings').filterwarnings("ignore", category=UserWarning))
warnings_filter.start()
warnings_filter.join()
tf.get_logger().setLevel('ERROR')

def fetch_top_usdt_futures_symbols(topn=20):
    ex_url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    tick_url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
    try:
        ex_resp = requests.get(ex_url, timeout=10)
        ex_resp.raise_for_status()
        tick_resp = requests.get(tick_url, timeout=10)
        tick_resp.raise_for_status()
        ex_data = ex_resp.json()
        tick_data = tick_resp.json()
        symbols_info = {s["symbol"]: s for s in ex_data["symbols"] if (
            s["quoteAsset"] == "USDT" and s["status"] == "TRADING"
            and s.get("contractType", None) in ("PERPETUAL", "CURRENT_QUARTER", "NEXT_QUARTER")
            and s.get("marginAsset", None) == "USDT"
            and s.get("isMarginTradingAllowed", True) and not s.get("isInverse", False)
        )}
        tickers = [(t["symbol"], float(t["quoteVolume"])) for t in tick_data if t["symbol"] in symbols_info]
        top_symbols = [sym for sym, _ in sorted(tickers, key=lambda x: -x[1])[:topn]]
        return top_symbols
    except Exception as e:
        print("[TEST] Lỗi fetch symbols/ticker:", e)
        return []

INTERVALS = ["5m", "15m"]
LIMIT = 300
N_PAST = 30
N_EPOCHS = 10
FEATURES = ["close", "rsi", "ma", "macd"]
MIN_DIFF_PCT = 0.05
BALANCE = 1000

def suggest_leverage(diff_pct, min_lev=2, max_lev=12):
    pct = abs(diff_pct)
    if pct >= 1:
        return max_lev
    elif pct >= 0.5:
        return 10
    elif pct >= 0.25:
        return 7
    elif pct >= 0.1:
        return 5
    elif pct >= 0.05:
        return 3
    else:
        return min_lev

open_orders = dict()

def add_indicators(df):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['ma'] = closes.rolling(14).mean()
    exp1 = closes.ewm(span=12, adjust=False).mean()
    exp2 = closes.ewm(span=26, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def fetch_realtime_klines(symbol, interval, limit):
    url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}"
    resp = requests.get(url)
    data = resp.json()
    df = pd.DataFrame(data, columns=[
        "open_time", "open", "high", "low", "close", "volume", "close_time",
        "quote_asset_vol", "num_trades", "taker_buy_base_vol", "taker_buy_quote_vol", "ignore"
    ])
    for col in ["open", "high", "low", "close", "volume"]:
        df[col] = df[col].astype(float)
    df["open_time"] = df["open_time"].astype(int)
    return df

def manage_order(symbol, interval, last_close):
    key = (symbol, interval)
    if key in open_orders:
        order = open_orders[key]
        side = order['side']
        sl = order['sl']
        tp = order['tp']
        entry = order['entry']
        leverage = order.get('leverage', 10)
        quantity = order.get('size', 0)
        if (side == 'LONG' and last_close <= sl) or (side == 'SHORT' and last_close >= sl):
            print(f"[ORDER] {symbol}-{interval}: HIT STOP-LOSS! Đóng lệnh {side} x{leverage}, Entry={entry}, SL={sl}, Giá hiện tại={last_close}")
            close_position(symbol, 'BUY' if side == 'LONG' else 'SELL')
            del open_orders[key]
        elif (side == 'LONG' and last_close >= tp) or (side == 'SHORT' and last_close <= tp):
            print(f"[ORDER] {symbol}-{interval}: TAKE-PROFIT! Đóng lệnh {side} x{leverage}, Entry={entry}, TP={tp}, Giá hiện tại={last_close}")
            close_position(symbol, 'BUY' if side == 'LONG' else 'SELL')
            del open_orders[key]

def test_pipeline_realtime():
    SYMBOLS = fetch_top_usdt_futures_symbols(topn=20)
    print(f"[TEST] Quét top {len(SYMBOLS)} cặp futures USDT volume lớn...")
    db = get_database()
    while True:
        for symbol in SYMBOLS:
            for interval in INTERVALS:
                collection_name = f"kline_{symbol.lower()}_{interval}"
                collection = db[collection_name]
                data = list(collection.find().sort('open_time', -1).limit(LIMIT))
                data = data[::-1]
                if not data or len(data) < LIMIT:
                    print(f"[TEST] {symbol}-{interval}: Fetching real-time candles...")
                    df = fetch_realtime_klines(symbol, interval, limit=LIMIT)
                else:
                    df = pd.DataFrame(data)
                df = add_indicators(df)
                df = df.dropna()
                if len(df) < N_PAST + 1:
                    print(f"[TEST] {symbol}-{interval}: Không đủ dữ liệu sạch sau khi tính chỉ báo.")
                    continue
                model_path = train_and_save_lstm_multi(df, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
                print(f"[TEST] {symbol}-{interval}: Train xong, lưu model tại {model_path}")
                pred = load_and_predict_lstm_multi(df, FEATURES, n_past=N_PAST)
                if pred is None or (isinstance(pred, float) and np.isnan(pred)):
                    print(f"[TEST] {symbol}-{interval}: Model predict lỗi hoặc không đủ dữ liệu.")
                    continue
                last_row = df.iloc[-1]
                last_close = float(last_row["close"])
                manage_order(symbol, interval, last_close)
                diff_pct = ((pred - last_close) / last_close) * 100 if pred else 0
                leverage = suggest_leverage(diff_pct)
                if diff_pct > MIN_DIFF_PCT and (symbol, interval) not in open_orders:
                    signal_type = "BUY"
                    side = "LONG"
                elif diff_pct < -MIN_DIFF_PCT and (symbol, interval) not in open_orders:
                    signal_type = "SELL"
                    side = "SHORT"
                else:
                    signal_type = "HOLD"
                    side = None
                signal = {
                    "symbol": symbol,
                    "interval": interval,
                    "last_close": last_close,
                    "prediction": pred,
                    "diff_pct": diff_pct,
                    "signal": signal_type,
                    "side": side,
                    "leverage": leverage,
                    "features": {f: float(last_row[f]) if f in last_row else None for f in FEATURES},
                    "timestamp": int(time.time()*1000)
                }
                if signal_type != "HOLD" and (symbol, interval) not in open_orders:
                    sl_tp = check_stop_loss_take_profit(signal)
                    pos_size = check_position_size(BALANCE)
                    log_trade_signal(
                        symbol, interval, signal_type, last_close, pred,
                        sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
                    )
                    open_orders[(symbol, interval)] = {
                        'side': side,
                        'entry': last_close,
                        'sl': sl_tp['stop_loss'],
                        'tp': sl_tp['take_profit'],
                        'size': pos_size,
                        'leverage': leverage,
                        'order_time': int(time.time())
                    }
                    # TÍCH HỢP ĐẶT LỆNH THỰC CHIẾN VÀO ĐÂY!
                    side_binance = 'BUY' if side == 'LONG' else 'SELL'
                    try:
                        open_position(symbol, side_binance, pos_size, entry_price=last_close, sl=sl_tp['stop_loss'], tp=sl_tp['take_profit'], leverage=leverage)
                    except Exception as e:
                        print(f"[ORDER] Lỗi đặt lệnh thực chiến: {e}")
                    print(f"[ORDER] {symbol}-{interval}: MỞ LỆNH {side} x{leverage} | Entry={last_close:.2f} | SL={sl_tp['stop_loss']:.2f} | TP={sl_tp['take_profit']:.2f} | Size={pos_size:.2f}")
                print(f"[TEST] {symbol}-{interval}: Signal={signal_type}, Side={side}, Leverage={leverage}, Close={last_close}, Predict={pred}, Diff={diff_pct:.3f}%")
                bt = backtest_signals(symbol, interval, n_recent=100)
                if 'winrate' in bt:
                    print(f"[TEST] {symbol}-{interval}: Backtest: Winrate={bt['winrate']:.2f}%, Total profit={bt['total_profit']:.4f}")
                else:
                    print(f"[TEST] {symbol}-{interval}: Backtest error: {bt.get('error', 'Không đủ dữ liệu để backtest')}")
        print("[TEST] Ngủ 60s chờ quét realtime tiếp...")
        time.sleep(60)

if __name__ == "__main__":
    test_pipeline_realtime()
