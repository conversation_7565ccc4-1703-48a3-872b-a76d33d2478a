
from pymongo.database import Database
from typing import Any, Dict, List, Optional

class CRUDManager:
    def __init__(self, db: Database, collection_name: str):
        self.collection = db[collection_name]

    def create(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Inserts a single document into the collection."""
        result = self.collection.insert_one(document)
        document["_id"] = str(result.inserted_id) # Convert ObjectId to string for easier handling
        return document

    def read_one(self, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Finds a single document matching the query."""
        document = self.collection.find_one(query)
        if document:
            document["_id"] = str(document["_id"])
        return document

    def read_many(self, query: Dict[str, Any] = None, sort: Optional[List[tuple]] = None, limit: int = 0) -> List[Dict[str, Any]]:
        """
        Finds multiple documents matching the query.
        sort: list of (field, direction) tuples, e.g., [("timestamp", 1)]
        limit: maximum number of documents to return
        """
        if query is None:
            query = {}
        cursor = self.collection.find(query)
        if sort:
            cursor = cursor.sort(sort)
        if limit > 0:
            cursor = cursor.limit(limit)
        
        documents = []
        for doc in cursor:
            doc["_id"] = str(doc["_id"])
            documents.append(doc)
        return documents

    def update_one(self, query: Dict[str, Any], new_values: Dict[str, Any]) -> int:
        """Updates a single document matching the query."""
        result = self.collection.update_one(query, {"$set": new_values})
        return result.modified_count

    def update_many(self, query: Dict[str, Any], new_values: Dict[str, Any]) -> int:
        """Updates multiple documents matching the query."""
        result = self.collection.update_many(query, {"$set": new_values})
        return result.modified_count

    def delete_one(self, query: Dict[str, Any]) -> int:
        """Deletes a single document matching the query."""
        result = self.collection.delete_one(query)
        return result.deleted_count

    def delete_many(self, query: Dict[str, Any]) -> int:
        """Deletes multiple documents matching the query."""
        result = self.collection.delete_many(query)
        return result.deleted_count

# Example Usage (for testing purposes)
if __name__ == '__main__':
    from db.session import get_database
    db = get_database()
    
    # Example for a 'users' collection
    user_crud = CRUDManager(db, "users")

    # Create a user
    print("\n--- Creating User ---")
    new_user = {"username": "testuser", "email": "<EMAIL>", "balance": 1000}
    created_user = user_crud.create(new_user)
    print(f"Created user: {created_user}")

    # Read a user
    print("\n--- Reading User ---")
    found_user = user_crud.read_one({"username": "testuser"})
    print(f"Found user: {found_user}")

    # Update a user
    print("\n--- Updating User ---")
    updated_count = user_crud.update_one({"username": "testuser"}, {"balance": 1200})
    print(f"Updated {updated_count} user(s).")
    found_user_after_update = user_crud.read_one({"username": "testuser"})
    print(f"User after update: {found_user_after_update}")

    # Create another user
    print("\n--- Creating Another User ---")
    another_user = {"username": "anotheruser", "email": "<EMAIL>", "balance": 500}
    user_crud.create(another_user)

    # Read many users
    print("\n--- Reading Many Users ---")
    all_users = user_crud.read_many()
    print(f"All users: {all_users}")

    # Delete a user
    print("\n--- Deleting User ---")
    deleted_count = user_crud.delete_one({"username": "testuser"})
    print(f"Deleted {deleted_count} user(s).")

    # Verify deletion
    print("\n--- Verifying Deletion ---")
    remaining_users = user_crud.read_many()
    print(f"Remaining users: {remaining_users}")

    # Clean up (delete all test users)
    user_crud.delete_many({"email": {"$in": ["<EMAIL>", "<EMAIL>"]}})
    print("Cleaned up test users.")


