
# risk_management/risk_rules.py
# Module quản lý rủi ro: kiểm tra SL/TP, DCA, position sizing cho tín hiệu trade

from typing import Optional, Dict

def calculate_stop_loss_take_profit(last_close: float, direction: str, sl_pct: float = 1.5, tp_pct: float = 2.0) -> Optional[Dict[str, float]]:
    """
    Tính toán các mức giá SL/TP dựa trên giá đóng cửa gần nhất và hướng giao dịch.
    - sl_pct: phần trăm rủi ro cho Stop Loss.
    - tp_pct: phần trăm mục tiêu lợi nhuận cho Take Profit.
    """
    if last_close is None or direction not in ("BUY", "SELL"):
        return None

    if direction == "BUY":
        sl = last_close * (1 - sl_pct / 100)
        tp = last_close * (1 + tp_pct / 100)
    else:  # SELL
        sl = last_close * (1 + sl_pct / 100)
        tp = last_close * (1 - tp_pct / 100)

    return {"stop_loss": round(sl, 8), "take_profit": round(tp, 8)}

def calculate_position_size(balance: float, risk_pct: float = 1.0, sl_price: float = None, entry_price: float = None, symbol_price: float = None) -> float:
    """
    Quản lý khối lượng lệnh dựa trên rủi ro tài khoản và khoảng cách đến Stop Loss.
    - balance: số dư hiện tại (USD).
    - risk_pct: % tài khoản tối đa chịu rủi ro trên 1 lệnh.
    - sl_price: Mức giá Stop Loss của lệnh.
    - entry_price: Mức giá vào lệnh (nếu là lệnh Limit).
    - symbol_price: Giá hiện tại của tài sản (nếu là lệnh Market).
    """
    if balance <= 0 or risk_pct <= 0:
        return 0.0

    risk_amount = balance * (risk_pct / 100)

    if sl_price is None or entry_price is None and symbol_price is None:
        # Cannot calculate position size without SL and an entry/current price
        print("Warning: Cannot calculate position size without Stop Loss price and an entry/current price.")
        return 0.0

    actual_entry_price = entry_price if entry_price is not None else symbol_price

    if actual_entry_price is None or actual_entry_price == 0:
        print("Warning: Entry/Symbol price is zero or None, cannot calculate position size.")
        return 0.0

    # Calculate the percentage risk per share/unit
    price_difference = abs(actual_entry_price - sl_price)
    if price_difference == 0:
        print("Warning: Price difference between entry and SL is zero, cannot calculate position size.")
        return 0.0

    # Position size in units (e.g., BTC, ETH)
    # This assumes a 1:1 risk-reward ratio for simplicity in calculation of units
    # In practice, you'd divide risk_amount by the dollar value of the price_difference per unit
    # For example, if 1 BTC = $30,000 and SL is $1,000 away, then risk per BTC is $1,000.
    # So, units = risk_amount / price_difference
    position_size_units = risk_amount / price_difference

    # This calculation gives the quantity of the base asset (e.g., BTC for BTCUSDT)
    # You might need to adjust this based on the minimum order size and step size of Binance.
    return max(0.0, position_size_units)


# Alias functions for backward compatibility with tests
def check_stop_loss_take_profit(last_close: float, direction: str, sl_pct: float = 1.5, tp_pct: float = 2.0) -> Optional[Dict[str, float]]:
    """Alias for calculate_stop_loss_take_profit"""
    return calculate_stop_loss_take_profit(last_close, direction, sl_pct, tp_pct)

def check_position_size(balance: float, risk_pct: float = 1.0, sl_price: float = None, entry_price: float = None, symbol_price: float = None) -> float:
    """Alias for calculate_position_size"""
    return calculate_position_size(balance, risk_pct, sl_price, entry_price, symbol_price)

def apply_dca_logic(current_position_value: float, new_signal_price: float, dca_strategy: Dict) -> Optional[float]:
    """
    Applies Dollar-Cost Averaging (DCA) logic.
    dca_strategy: dictionary containing DCA parameters (e.g., {"enabled": True, "percentage_drop": 5, "max_dca_orders": 3})
    Returns the quantity to add for DCA, or None if DCA is not triggered.
    """
    if not dca_strategy.get("enabled", False):
        return None

    # Placeholder for DCA logic
    # You would need to track previous entry prices and number of DCA orders executed.
    # For example, if price drops by dca_strategy["percentage_drop"] from previous entry, trigger DCA.
    print("DCA logic placeholder: Implement actual DCA calculation here.")
    return None # Return actual quantity if DCA is triggered


# Example Usage
if __name__ == '__main__':
    # Test calculate_stop_loss_take_profit
    print("\n--- SL/TP Calculation ---")
    signal_buy = {"last_close": 30000, "signal": "BUY"}
    sl_tp_buy = calculate_stop_loss_take_profit(signal_buy["last_close"], signal_buy["signal"])
    print(f"BUY Signal SL/TP: {sl_tp_buy}")

    signal_sell = {"last_close": 30000, "signal": "SELL"}
    sl_tp_sell = calculate_stop_loss_take_profit(signal_sell["last_close"], signal_sell["signal"])
    print(f"SELL Signal SL/TP: {sl_tp_sell}")

    # Test calculate_position_size
    print("\n--- Position Size Calculation ---")
    account_balance = 10000.0
    risk_per_trade_pct = 1.0 # 1% of balance
    current_btc_price = 30000.0
    calculated_sl_price = 29700.0 # 1% below current price

    pos_size = calculate_position_size(
        balance=account_balance,
        risk_pct=risk_per_trade_pct,
        sl_price=calculated_sl_price,
        symbol_price=current_btc_price
    )
    print(f"Position size for {account_balance}$ balance, {risk_per_trade_pct}% risk, SL at {calculated_sl_price} (current price {current_btc_price}): {pos_size} units")

    # Test DCA logic (placeholder)
    print("\n--- DCA Logic ---")
    dca_config = {"enabled": True, "percentage_drop": 2, "max_dca_orders": 2}
    dca_quantity = apply_dca_logic(current_position_value=1000, new_signal_price=29500, dca_strategy=dca_config)
    print(f"DCA quantity: {dca_quantity}")
