
from pydantic import BaseModel
from datetime import datetime

class Candle(BaseModel):
    open_time: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    close_time: datetime
    quote_asset_volume: float
    number_of_trades: int
    taker_buy_base_asset_volume: float
    taker_buy_quote_asset_volume: float
    ignore: float # Or appropriate type for the last field

    class Config:
        json_encoders = {
            datetime: lambda v: v.timestamp() * 1000, # Convert datetime to milliseconds timestamp
        }
        # allow_population_by_field_name = True
        # arbitrary_types_allowed = True # Needed for custom types if any



