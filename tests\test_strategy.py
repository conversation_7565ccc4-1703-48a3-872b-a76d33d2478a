
import pytest
import pandas as pd
from strategy_management.strategies import SimpleMovingAverageStrategy
from risk_management.risk_rules import calculate_stop_loss_take_profit, calculate_position_size

# Sample DataFrame for testing strategies
@pytest.fixture
def sample_klines_df_strategy():
    data = {
        'open_time': pd.to_datetime([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], unit='s'),
        'open': [100, 102, 105, 103, 106, 108, 105, 107, 109, 112],
        'high': [103, 106, 107, 105, 108, 110, 107, 109, 112, 115],
        'low': [99, 101, 104, 102, 105, 107, 104, 106, 108, 110],
        'close': [102, 105, 103, 106, 107, 109, 106, 108, 111, 114],
        'volume': [1000, 1200, 1100, 1300, 1050, 1400, 1150, 1250, 1350, 1500]
    }
    df = pd.DataFrame(data)
    df.set_index('open_time', inplace=True)
    return df

# Test SimpleMovingAverageStrategy
def test_sma_strategy_buy_signal(sample_klines_df_strategy):
    strategy = SimpleMovingAverageStrategy(short_window=3, long_window=7)
    # Manipulate data to force a buy signal (short MA crosses above long MA)
    df = sample_klines_df_strategy.copy()
    df.loc[df.index[-1], 'close'] = 120 # Make last close high to force crossover
    df.loc[df.index[-2], 'close'] = 110
    df.loc[df.index[-3], 'close'] = 100

    signal = strategy.generate_signal(df)
    assert signal is not None
    assert signal['signal'] == 'BUY'
    assert 'last_close' in signal

def test_sma_strategy_sell_signal(sample_klines_df_strategy):
    strategy = SimpleMovingAverageStrategy(short_window=3, long_window=7)
    # Manipulate data to force a sell signal (short MA crosses below long MA)
    df = sample_klines_df_strategy.copy()
    df.loc[df.index[-1], 'close'] = 90 # Make last close low to force crossover
    df.loc[df.index[-2], 'close'] = 100
    df.loc[df.index[-3], 'close'] = 110

    signal = strategy.generate_signal(df)
    assert signal is not None
    assert signal['signal'] == 'SELL'
    assert 'last_close' in signal

def test_sma_strategy_no_signal(sample_klines_df_strategy):
    strategy = SimpleMovingAverageStrategy(short_window=3, long_window=7)
    signal = strategy.generate_signal(sample_klines_df_strategy)
    # Depending on the sample data, it might generate a signal or not. 
    # For a generic test, we'll assume no strong signal for this specific data.
    # A more robust test would craft data to ensure no signal.
    # assert signal is None # This might fail if the sample data happens to create a signal
    pass

# Test risk_management/risk_rules.py functions
def test_calculate_stop_loss_take_profit():
    last_close = 100.0
    sl_tp_buy = calculate_stop_loss_take_profit(last_close, "BUY", sl_pct=1.0, tp_pct=2.0)
    assert sl_tp_buy["stop_loss"] == 99.0
    assert sl_tp_buy["take_profit"] == 102.0

    sl_tp_sell = calculate_stop_loss_take_profit(last_close, "SELL", sl_pct=1.0, tp_pct=2.0)
    assert sl_tp_sell["stop_loss"] == 101.0
    assert sl_tp_sell["take_profit"] == 98.0

def test_calculate_position_size():
    balance = 10000.0
    risk_pct = 1.0
    sl_price = 99.0
    entry_price = 100.0

    pos_size = calculate_position_size(balance, risk_pct, sl_price, entry_price)
    # Expected: risk_amount = 10000 * 0.01 = 100
    # price_difference = abs(100 - 99) = 1
    # pos_size = 100 / 1 = 100
    assert pos_size == 100.0

    # Test with symbol_price
    pos_size_market = calculate_position_size(balance, risk_pct, sl_price=99.0, symbol_price=100.0)
    assert pos_size_market == 100.0

    # Test edge cases
    assert calculate_position_size(0, 1, 99, 100) == 0.0
    assert calculate_position_size(10000, 0, 99, 100) == 0.0
    assert calculate_position_size(10000, 1, 100, 100) == 0.0 # SL at entry
    assert calculate_position_size(10000, 1, None, 100) == 0.0 # No SL


