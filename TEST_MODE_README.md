# 🤖 Binance Trading Bot - Test Mode Guide

## 🔒 Test Mode Overview

Test Mode cho phép bạn chạy bot với dữ liệu real-time từ Binance nhưng **KHÔNG thực hiện giao dịch thật**. <PERSON><PERSON><PERSON> là cách an toàn nhất để test bot trước khi sử dụng với tiền thật.

## ⚙️ Cấu hình Test Mode

### 1. File .env
Đảm bảo file `.env` có cấu hình sau:
```env
# Binance API (có thể để placeholder)
BINANCE_API_KEY=xxx
BINANCE_API_SECRET=yyy
TEST_MODE=1  # ← Quan trọng: Bật test mode

# MongoDB
MONGO_URI=mongodb+srv://thanh217:<EMAIL>/
MONGO_DB_NAME=dataBot

# CORS
CORS_ORIGIN=http://localhost:3000
```

### 2. Khởi động FastAPI Server
```bash
# Kích hoạt virtual environment
venv\Scripts\activate  # Windows
# hoặc
source venv/bin/activate  # Linux/Mac

# Chạy server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🧪 Các Script Test

### 1. Quick Test - Test nhanh một chu kỳ
```bash
python quick_test.py
```
- Phân tích BTCUSDT, ETHUSDT, BNBUSDT
- Hiển thị giá hiện tại và chỉ báo kỹ thuật
- Tạo tín hiệu giao dịch demo

### 2. API Endpoints Test - Test tất cả API
```bash
python test_api_endpoints.py
```
- Test 10 API endpoints chính
- Kiểm tra kết nối MongoDB
- Lưu dữ liệu demo vào database

### 3. Real-time Monitor - Dashboard trực tiếp
```bash
python realtime_monitor.py
```
- Dashboard cập nhật mỗi 30 giây
- Hiển thị giá, RSI, MA, MACD
- Hiển thị tín hiệu giao dịch gần nhất
- Nhấn Ctrl+C để dừng

### 4. Comprehensive Test Suite - Chạy tất cả test
```bash
python run_tests.py
```
- Chạy tất cả test tự động
- Báo cáo tổng hợp kết quả
- Kiểm tra server trước khi test

### 5. Real-time Demo Bot - Bot chạy liên tục
```bash
python test_realtime_demo.py
```
- Chạy bot liên tục mỗi 5 phút
- Phân tích và tạo tín hiệu
- Log tất cả hoạt động
- Nhấn Ctrl+C để dừng

## 📊 Dữ liệu Test

### Symbols được monitor:
- **BTCUSDT** - Bitcoin
- **ETHUSDT** - Ethereum  
- **BNBUSDT** - Binance Coin
- **ADAUSDT** - Cardano
- **SOLUSDT** - Solana

### Chỉ báo kỹ thuật:
- **RSI** - Relative Strength Index
- **MA20/MA50** - Moving Averages
- **MACD** - Moving Average Convergence Divergence
- **Bollinger Bands** (trong full analysis)

### Tín hiệu giao dịch:
- **BUY** 🟢 - Tín hiệu mua
- **SELL** 🔴 - Tín hiệu bán  
- **HOLD** ⚪ - Giữ nguyên

## 🔐 Tính năng an toàn

### ✅ Những gì được thực hiện:
- Lấy dữ liệu giá real-time từ Binance
- Tính toán chỉ báo kỹ thuật
- Tạo tín hiệu giao dịch
- Lưu log vào MongoDB
- Hiển thị dashboard

### ❌ Những gì KHÔNG được thực hiện:
- Đặt lệnh mua/bán thật
- Sử dụng tiền thật
- Kết nối với Binance trading API
- Thay đổi balance thật

## 🚀 Quy trình Test Khuyến nghị

### Bước 1: Test cơ bản
```bash
python quick_test.py
```

### Bước 2: Test API đầy đủ
```bash
python test_api_endpoints.py
```

### Bước 3: Monitor real-time
```bash
python realtime_monitor.py
```

### Bước 4: Test bot liên tục (tùy chọn)
```bash
python test_realtime_demo.py
```

## 📈 Hiểu kết quả

### RSI (Relative Strength Index):
- **> 70**: Overbought (có thể bán) 🔴
- **< 30**: Oversold (có thể mua) 🟢
- **30-70**: Neutral 🟡

### Moving Average Signals:
- **Giá > MA20 > MA50**: Xu hướng tăng
- **Giá < MA20 < MA50**: Xu hướng giảm

### MACD:
- **MACD > Signal**: Tín hiệu tăng
- **MACD < Signal**: Tín hiệu giảm

## 🛠️ Troubleshooting

### Lỗi thường gặp:

1. **Cannot connect to API**
   ```bash
   # Kiểm tra server có chạy không
   curl http://localhost:8000/
   ```

2. **Module not found**
   ```bash
   # Cài đặt dependencies
   pip install -r requirements.txt
   ```

3. **MongoDB connection error**
   - Kiểm tra MONGO_URI trong .env
   - Kiểm tra kết nối internet

4. **Binance API error**
   - Dữ liệu vẫn hoạt động với API public
   - Không cần API key cho test mode

## 💡 Tips

1. **Chạy test trong giờ thị trường hoạt động** để có dữ liệu tốt nhất
2. **Monitor ít nhất 30 phút** để thấy các tín hiệu khác nhau
3. **Kiểm tra log trong MongoDB** để theo dõi lịch sử
4. **So sánh tín hiệu với thị trường thật** để đánh giá độ chính xác

## 🔄 Chuyển sang Live Trading

Khi sẵn sàng chuyển sang live trading:

1. Cập nhật API keys thật trong `.env`
2. Đổi `TEST_MODE=0`
3. Test với số tiền nhỏ trước
4. Monitor cẩn thận các lệnh đầu tiên

---

**⚠️ LƯU Ý: Luôn giữ TEST_MODE=1 khi testing để tránh giao dịch nhầm!**
