#!/usr/bin/env python3
"""
Simple Test - Test đơn giản không dùng emoji
"""

import requests
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Import các module của bot
from risk_management.risk_rules import calculate_stop_loss_take_profit, calculate_position_size
from db.session import get_database

load_dotenv()

def get_current_price(symbol):
    """Lấy giá hiện tại từ Binance"""
    try:
        url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            return float(data['price'])
    except Exception as e:
        print(f"Error getting price: {e}")
    return None

def get_kline_data(symbol, interval="5m", limit=100):
    """Lấy dữ liệu kline từ Binance"""
    try:
        url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}"
        response = requests.get(url)
        
        if response.status_code == 200:
            klines = response.json()
            
            # <PERSON>yển đổi thành DataFrame
            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
            
            return df
    except Exception as e:
        print(f"Error getting kline data: {e}")
    return None

def calculate_indicators(df):
    """Tính các chỉ báo kỹ thuật"""
    if df is None or len(df) < 50:
        return df
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Moving Averages
    df['ma_20'] = df['close'].rolling(20).mean()
    df['ma_50'] = df['close'].rolling(50).mean()
    
    # MACD
    exp1 = df['close'].ewm(span=12).mean()
    exp2 = df['close'].ewm(span=26).mean()
    df['macd'] = exp1 - exp2
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    
    return df

def generate_simple_signal(df):
    """Tạo tín hiệu đơn giản dựa trên MA và RSI"""
    if df is None or len(df) < 50:
        return None
    
    latest = df.iloc[-1]
    prev = df.iloc[-2]
    
    # Điều kiện BUY: MA20 > MA50 và RSI < 70 và giá vượt MA20
    if (latest['ma_20'] > latest['ma_50'] and 
        latest['rsi'] < 70 and 
        latest['close'] > latest['ma_20'] and
        prev['close'] <= prev['ma_20']):
        return "BUY"
    
    # Điều kiện SELL: MA20 < MA50 và RSI > 30 và giá dưới MA20
    elif (latest['ma_20'] < latest['ma_50'] and 
          latest['rsi'] > 30 and 
          latest['close'] < latest['ma_20'] and
          prev['close'] >= prev['ma_20']):
        return "SELL"
    
    return "HOLD"

def test_symbol(symbol):
    """Test một symbol"""
    print(f"\n[INFO] Analyzing {symbol}...")
    
    # Lấy giá hiện tại
    current_price = get_current_price(symbol)
    if current_price:
        print(f"[PRICE] Current Price: ${current_price:.4f}")
    
    # Lấy dữ liệu kline
    df = get_kline_data(symbol)
    if df is None:
        print("[ERROR] Failed to get kline data")
        return
    
    # Tính chỉ báo
    df = calculate_indicators(df)
    
    # Lấy dữ liệu mới nhất
    latest = df.iloc[-1]
    
    print(f"[INDICATORS] Technical Indicators:")
    print(f"   RSI: {latest['rsi']:.2f}")
    print(f"   MA20: ${latest['ma_20']:.4f}")
    print(f"   MA50: ${latest['ma_50']:.4f}")
    print(f"   MACD: {latest['macd']:.6f}")
    
    # Tạo tín hiệu
    signal = generate_simple_signal(df)
    print(f"[SIGNAL] Signal: {signal}")
    
    if signal != "HOLD":
        # Tính SL/TP
        sl_tp = calculate_stop_loss_take_profit(latest['close'], signal)
        
        # Tính position size
        demo_balance = 10000.0
        position_size = calculate_position_size(
            balance=demo_balance,
            risk_pct=1.0,
            sl_price=sl_tp['stop_loss'] if sl_tp else None,
            symbol_price=latest['close']
        )
        
        print(f"[TRADE] Trade Details:")
        print(f"   Entry Price: ${latest['close']:.4f}")
        print(f"   Stop Loss: ${sl_tp['stop_loss']:.4f}" if sl_tp else "   Stop Loss: N/A")
        print(f"   Take Profit: ${sl_tp['take_profit']:.4f}" if sl_tp else "   Take Profit: N/A")
        print(f"   Position Size: {position_size:.6f} {symbol[:-4]}")
        print(f"   Risk Amount: ${demo_balance * 0.01:.2f} (1% of balance)")

def test_api_endpoint(endpoint, params=None):
    """Test một API endpoint"""
    try:
        url = f"http://localhost:8000{endpoint}"
        response = requests.get(url, params=params, timeout=10)
        
        print(f"[API] GET {endpoint}")
        print(f"[STATUS] {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"[SUCCESS] API call successful")
            return result
        else:
            print(f"[ERROR] API call failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"[EXCEPTION] API call exception: {e}")
        return None

def main():
    """Main function"""
    print("=" * 60)
    print("BINANCE TRADING BOT - SIMPLE TEST")
    print("=" * 60)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("TEST MODE: No real trades will be executed")
    print()
    
    # Test 1: API Health Check
    print("[TEST 1] API Health Check")
    result = test_api_endpoint("/")
    if result:
        print(f"   Message: {result.get('msg')}")
    print()
    
    # Test 2: Current Prices
    print("[TEST 2] Current Prices")
    result = test_api_endpoint("/prices", {"symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"]})
    if result and result.get('data'):
        for item in result['data']:
            if item.get('success'):
                print(f"   {item.get('symbol')}: ${item.get('price')}")
    print()
    
    # Test 3: Technical Analysis
    print("[TEST 3] Technical Analysis")
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
    
    for symbol in symbols:
        try:
            test_symbol(symbol)
        except Exception as e:
            print(f"[ERROR] Error testing {symbol}: {e}")
    
    print("\n" + "=" * 60)
    print("[COMPLETE] Simple test completed!")
    print("[INFO] Bot is working with real-time data")
    print("[SAFE] No real trades executed - TEST MODE")
    print("=" * 60)

if __name__ == "__main__":
    main()
